#!/bin/bash
{
  while IFS= read -r -d '' file; do
    relative="${file#./}"

    if [[ "$relative" == "index.ts" ]]; then
      continue
    fi

    if [[ "$relative" == */index.ts ]]; then
      dir=$(dirname "$relative")
      echo "export * from \"./$dir\";"
    elif [[ "$relative" == *.tsx ]]; then
      path="${relative%.tsx}"
      echo "export * from \"./$path\";"
    fi
  done < <(find . -type f \( -name "index.ts" -o -name "*.tsx" \) -print0)
} | sort -u > index.ts
