import { NextRequest } from 'next/server';
import { proxyGet, proxyPut } from '@/lib/apiProxy';

/**
 * GET /api/users/profile
 * Proxy endpoint to get user profile
 * Requires: Authorization: Bearer <token> header
 */
export async function GET(request: NextRequest) {
  return proxyGet(request, '/users/profile', true);
}

/**
 * PUT /api/users/profile
 * Proxy endpoint to update user profile
 * Requires: Authorization: Bearer <token> header
 * Expects: Partial<UserProfile> in request body
 */
export async function PUT(request: NextRequest) {
  return proxyPut(request, '/users/profile', true);
}
