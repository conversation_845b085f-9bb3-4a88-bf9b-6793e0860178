/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { EXTERNAL_API_URL } from '@/constants/api';

/**
 * Server-side HTTP service for making API calls from Next.js API routes
 * This service is used to proxy requests to the external API
 */
class ServerHttpService {
  private readonly axiosInstance: AxiosInstance;

  constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000, // 30 seconds timeout
    });

    this.initializeInterceptors();
  }

  private initializeInterceptors() {
    // Request interceptor for logging
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(`[ServerHttp] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[ServerHttp] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging and error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        console.log(`[ServerHttp] ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('[ServerHttp] Response error:', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a GET request with optional authorization token
   */
  public async get<T>(url: string, token?: string, config?: AxiosRequestConfig): Promise<T> {
    const headers = this.buildHeaders(token);
    const response = await this.axiosInstance.get<T>(url, { ...config, headers });
    return response.data;
  }

  /**
   * Make a POST request with optional authorization token
   */
  public async post<T>(
    url: string,
    data?: any,
    token?: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const headers = this.buildHeaders(token);
    const response = await this.axiosInstance.post<T>(url, data, { ...config, headers });
    return response.data;
  }

  /**
   * Make a PUT request with optional authorization token
   */
  public async put<T>(
    url: string,
    data?: any,
    token?: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const headers = this.buildHeaders(token);
    const response = await this.axiosInstance.put<T>(url, data, { ...config, headers });
    return response.data;
  }

  /**
   * Make a DELETE request with optional authorization token
   */
  public async delete<T>(url: string, token?: string, config?: AxiosRequestConfig): Promise<T> {
    const headers = this.buildHeaders(token);
    const response = await this.axiosInstance.delete<T>(url, { ...config, headers });
    return response.data;
  }

  /**
   * Build headers with optional authorization token
   */
  private buildHeaders(token?: string): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }
}

// Create singleton instance
const serverHttpService = new ServerHttpService(EXTERNAL_API_URL);

export default serverHttpService;
