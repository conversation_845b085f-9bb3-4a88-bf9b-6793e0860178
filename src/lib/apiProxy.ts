/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import serverHttpService from './serverHttp';

/**
 * Extract authorization token from request headers
 */
export function extractTokenFromRequest(request: NextRequest): string | undefined {
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  return undefined;
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  message: string,
  status: number = 500,
  details?: any
): NextResponse {
  return NextResponse.json(
    {
      error: message,
      details,
      timestamp: new Date().toISOString(),
    },
    { status }
  );
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(data: T, status: number = 200): NextResponse {
  return NextResponse.json(data, { status });
}

/**
 * Generic proxy handler for GET requests
 */
export async function proxyGet(
  request: NextRequest,
  endpoint: string,
  requireAuth: boolean = true
): Promise<NextResponse> {
  try {
    const token = extractTokenFromRequest(request);
    
    if (requireAuth && !token) {
      return createErrorResponse('Authorization token required', 401);
    }

    // Extract query parameters from the request URL
    const { searchParams } = new URL(request.url);
    const queryString = searchParams.toString();
    const fullEndpoint = queryString ? `${endpoint}?${queryString}` : endpoint;

    const data = await serverHttpService.get(fullEndpoint, token);
    return createSuccessResponse(data);
  } catch (error: any) {
    console.error(`[ProxyGet] Error for ${endpoint}:`, error);
    
    if (error.response) {
      return createErrorResponse(
        error.response.data?.message || 'External API error',
        error.response.status,
        error.response.data
      );
    }
    
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * Generic proxy handler for POST requests
 */
export async function proxyPost(
  request: NextRequest,
  endpoint: string,
  requireAuth: boolean = true
): Promise<NextResponse> {
  try {
    const token = extractTokenFromRequest(request);
    
    if (requireAuth && !token) {
      return createErrorResponse('Authorization token required', 401);
    }

    const body = await request.json().catch(() => ({}));
    const data = await serverHttpService.post(endpoint, body, token);
    return createSuccessResponse(data);
  } catch (error: any) {
    console.error(`[ProxyPost] Error for ${endpoint}:`, error);
    
    if (error.response) {
      return createErrorResponse(
        error.response.data?.message || 'External API error',
        error.response.status,
        error.response.data
      );
    }
    
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * Generic proxy handler for PUT requests
 */
export async function proxyPut(
  request: NextRequest,
  endpoint: string,
  requireAuth: boolean = true
): Promise<NextResponse> {
  try {
    const token = extractTokenFromRequest(request);
    
    if (requireAuth && !token) {
      return createErrorResponse('Authorization token required', 401);
    }

    const body = await request.json().catch(() => ({}));
    const data = await serverHttpService.put(endpoint, body, token);
    return createSuccessResponse(data);
  } catch (error: any) {
    console.error(`[ProxyPut] Error for ${endpoint}:`, error);
    
    if (error.response) {
      return createErrorResponse(
        error.response.data?.message || 'External API error',
        error.response.status,
        error.response.data
      );
    }
    
    return createErrorResponse('Internal server error', 500);
  }
}

/**
 * Generic proxy handler for DELETE requests
 */
export async function proxyDelete(
  request: NextRequest,
  endpoint: string,
  requireAuth: boolean = true
): Promise<NextResponse> {
  try {
    const token = extractTokenFromRequest(request);
    
    if (requireAuth && !token) {
      return createErrorResponse('Authorization token required', 401);
    }

    const data = await serverHttpService.delete(endpoint, token);
    return createSuccessResponse(data);
  } catch (error: any) {
    console.error(`[ProxyDelete] Error for ${endpoint}:`, error);
    
    if (error.response) {
      return createErrorResponse(
        error.response.data?.message || 'External API error',
        error.response.status,
        error.response.data
      );
    }
    
    return createErrorResponse('Internal server error', 500);
  }
}
