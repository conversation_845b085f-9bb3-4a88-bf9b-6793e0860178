import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserState, UserProfile } from '@/types/auth';

// Define the initial state
const initialState: UserState = {
  profile: null,
  loading: false,
  error: null,
};

// Create the user slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserProfile: (state, action: PayloadAction<UserProfile | Partial<UserProfile>>) => {
      if (state.profile) {
        // Update existing profile with new data
        state.profile = { ...state.profile, ...action.payload };
      } else if ('id' in action.payload) {
        // Only set as full profile if it has an ID
        state.profile = action.payload as UserProfile;
      } else {
        // Otherwise, set as partial profile
        state.profile = action.payload as UserProfile;
      }
    },
    clearUserProfile: (state) => {
      state.profile = null;
    },
    setUserError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

// Export actions and reducer
export const { setUserProfile, clearUserProfile, setUserError } = userSlice.actions;
export const userReducer = userSlice.reducer;