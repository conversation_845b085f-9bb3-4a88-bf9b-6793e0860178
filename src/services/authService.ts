import { EXTERNAL_ENDPOINTS } from '@/constants/endpoints';
import httpService from './http';
import { UserProfile } from '@/types/auth';
import { tokenUtils } from '@/utils/token';

export const authService = {
  // Extract token from URL
  extractTokenFromUrl(): string | null {
    if (typeof window !== 'undefined') {
      return tokenUtils.extractTokenFromUrl();
    }
    return null;
  },

  // Check if token is valid and not expired
  isTokenValid(token: string | null | undefined): boolean {
    return typeof token === 'string' && token.length > 0 && !tokenUtils.isTokenExpired(token);
  },

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<{ token: string; refreshToken: string }> {
    return httpService.post<{ token: string; refreshToken: string }>( EXTERNAL_ENDPOINTS.AUTH.REFRESH, { refreshToken });
  },

  // Verify token
  async verifyToken(token: string): Promise<boolean> {
    try {
      const response = await httpService.get<{ valid: boolean }>(EXTERNAL_ENDPOINTS.AUTH.VERIFY, { params: { authToken: token } });
      return response.valid;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return false;
    }
  },

  // Get user data from token
  getUserFromToken(token: string): Partial<UserProfile> | null {
    const userProfile = tokenUtils.extractUserProfile(token);
    if (!userProfile) return null;
    
    return userProfile;
  }
};