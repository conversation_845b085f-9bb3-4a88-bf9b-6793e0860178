/**
 * API Endpoints Configuration
 * 
 * This file defines all available API endpoints for both internal (Next.js proxy)
 * and external (direct backend) API calls.
 */

// Internal API endpoints (Next.js proxy routes)
export const INTERNAL_ENDPOINTS = {
  // Health check
  HEALTH: '/api/health',

  // Authentication endpoints
  AUTH: {
    VERIFY: '/api/auth/verify',
    REFRESH: '/api/auth/refresh',
  },

  // Other endpoints goes here...

} as const;

// External API endpoints (direct backend calls - used by server-side proxy)
export const EXTERNAL_ENDPOINTS = {
  HEALTH: '/health',

  // Authentication endpoints
  AUTH: {
    VERIFY: '/auth/verify',
    REFRESH: '/auth/refresh',
  },

  // Other endpoints...

} as const;

// API endpoint types for type safety
export type InternalEndpoint = typeof INTERNAL_ENDPOINTS;
export type ExternalEndpoint = typeof EXTERNAL_ENDPOINTS;

// Helper function to get internal endpoint
export function getInternalEndpoint(path: keyof typeof INTERNAL_ENDPOINTS): string {
  return INTERNAL_ENDPOINTS[path] as string;
}

// Helper function to get external endpoint
export function getExternalEndpoint(path: keyof typeof EXTERNAL_ENDPOINTS): string {
  return EXTERNAL_ENDPOINTS[path] as string;
}
