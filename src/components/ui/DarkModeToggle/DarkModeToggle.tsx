import { Moon, Sun } from 'lucide-react';
import { useEffect, useState } from 'react';

export const DarkModeToggle = () => {
    const [isDark, setIsDark] = useState(false);

    useEffect(() => {
        const html = document.querySelector('html');
        setIsDark(html?.dataset.theme === 'dark');
    }, []);

    const handleToggle = () => {
        const html = document.querySelector('html');
        const newIsDark = !isDark;
        setIsDark(newIsDark);
        
        if (html) {
            if (newIsDark) {
                html.setAttribute('data-theme', 'dark');
            } else {
                html.removeAttribute('data-theme');
            }
        }
    };

    return (
        <button
            onClick={handleToggle}
            className="p-2 rounded-full hover:bg-gray-800 transition-colors duration-200 ease-in-out"
            aria-label="Toggle dark mode"
        >
            <div className="relative w-5 h-5">
                <Sun 
                    className={`w-5 h-5 transition-all duration-300 ease-in-out transform ${
                        isDark ? 'opacity-0 rotate-90 scale-50' : 'opacity-100 rotate-0 scale-100'
                    } absolute top-0 left-0`}
                />
                <Moon 
                    className={`w-5 h-5 transition-all duration-300 ease-in-out transform ${
                        isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 rotate-90 scale-50'
                    } absolute top-0 left-0`}
                />
            </div>
        </button>
    );
};