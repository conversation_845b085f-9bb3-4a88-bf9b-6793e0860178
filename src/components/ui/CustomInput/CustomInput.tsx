'use client';

import { useState } from 'react';
import { EyeIcon, EyeSlashIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

interface InputProps {
    // Required props
    value: string;
    onChange: (value: string) => void;
    label: string;

    // Input type and content props
    type?: 'text' | 'password' | 'email' | 'number' | 'tel' | 'url';
    placeholder?: string;
    name?: string;
    id?: string;
    required?: boolean;
    disabled?: boolean;
    readOnly?: boolean;
    maxLength?: number;
    minLength?: number;
    pattern?: string;
    autoComplete?: string;
    showInfo?: boolean;
    infoText?: string;

    // Styling props
    className?: string;
    inputClassName?: string;
    labelClassName?: string;
    containerClassName?: string;
    height?: number;
    width?: number | 'full';
    padding?: string;
    marginBottom?: number;
    fontSize?: number;
    borderRadius?: number;
    borderColor?: string;
    focusBorderColor?: string;
    textColor?: string;
    labelColor?: string;
    backgroundColor?: string;
    error?: boolean;
    errorMessage?: string;
}

export const CustomInput = ({
    // Required props
    value,
    onChange,
    label,

    // Input type and content props
    type = 'text',
    placeholder = '',
    name,
    id,
    required = false,
    disabled = false,
    readOnly = false,
    maxLength,
    minLength,
    pattern,
    autoComplete,
    showInfo = false,
    infoText = '',

    // Styling props with defaults
    className = '',
    inputClassName = '',
    labelClassName = '',
    containerClassName = '',
    height = 35,
    width = 'full',
    padding = 'px-3 py-2',
    marginBottom = 20,
    fontSize = 14,
    borderRadius = 4,
    borderColor = '#CFD2DE',
    focusBorderColor = '#14b8a6',
    textColor = '#3B4154',
    labelColor = '#666F8F',
    backgroundColor = 'transparent',
    error = false,
    errorMessage,
}: InputProps) => {
    const [showPassword, setShowPassword] = useState(false);

    const isPassword = type === 'password';
    const isNumber = type === 'number';
    const inputType = isPassword ? (showPassword ? 'text' : 'password') : type;

    const containerStyles = {
        width: width === 'full' ? '100%' : `${width}px`,
        marginBottom: `${marginBottom}px`,
    };

    const inputStyles = {
        height: `${height}px`,
        fontSize: `${fontSize}px`,
        borderRadius: `${borderRadius}px`,
        borderColor: error ? 'red' : borderColor,
        color: textColor,
        backgroundColor,
    };

    const labelStyles = {
        color: labelColor,
        fontSize: `${fontSize}px`,
    };

    return (
        <div className={`w-full ${containerClassName}`} style={containerStyles}>
            <label
                className={`flex items-center ${labelClassName}`}
                style={labelStyles}
            >
                {label}
                {required && <span className="text-red-500">*</span>}
                {showInfo && (
                    <div className="tooltip relative ml-1 group">
                        <InformationCircleIcon className="w-4 h-4 text-gray-400" />
                        <span className="tooltiptext absolute invisible min-w-[200px] bg-white text-[#555] text-center rounded-[6px] p-[5px] shadow-[0px_0px_12px_#00000029] z-[100] bottom-[125%] left-1/2 -translate-x-1/2 opacity-0 transition-opacity duration-300 whitespace-nowrap group-hover:visible group-hover:opacity-100">
                            {infoText}
                        </span>
                    </div>
                )}
            </label>
            <div className="relative w-full">
                <input
                    type={inputType}
                    className={`w-full border text-sm rounded-[4px] focus:outline-none focus:border-teal-500 focus:ring-2 focus:ring-teal-500 ${padding} ${isNumber ? 'pr-8' : 'pr-10'} ${inputClassName} ${className}`}
                    style={inputStyles}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    placeholder={placeholder}
                    name={name}
                    id={id}
                    required={required}
                    disabled={disabled}
                    readOnly={readOnly}
                    maxLength={maxLength}
                    minLength={minLength}
                    pattern={pattern}
                    autoComplete={autoComplete}
                />
                {isPassword && (
                    <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-[#00B2A1]"
                    >
                        {showPassword ? <EyeSlashIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
                    </button>
                )}
            </div>
            {error && errorMessage && (
                <p className="text-red-500 text-xs mt-1">{errorMessage}</p>
            )}
        </div>
    );
}; 