// Authentication related types

// Auth state in Redux store
export interface AuthState {
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

// User state in Redux store
export interface UserState {
  profile: UserProfile | null;
  loading: boolean;
  error: string | null;
}

// User profile data
export interface UserProfile {
  id?: string;
  email: string;
  name: string;
  avatar?: string;
  role?: string;
  permissions?: string[];
  createdAt?: string;
  updatedAt?: string;
}

// Login request payload
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Login response from API
export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: UserProfile;
}

// Registration request payload
export interface RegisterCredentials {
  email: string;
  password: string;
  name: string;
}

// Token payload structure
export interface TokenPayload {
  sub: string; // user ID
  email: string;
  role: string;
  permissions?: string[];
  exp: number; // expiration timestamp
  iat: number; // issued at timestamp
}