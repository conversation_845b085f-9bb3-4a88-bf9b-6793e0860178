import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { AppDispatch, RootState } from '@/store/types';
import { verifyAndSetToken, clearAuth, setError } from '@/store/slices/authSlice';
import { setUserProfile } from '@/store/slices/userSlice';
import { tokenUtils } from '@/utils/token';

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const auth = useSelector((state: RootState) => state.auth);
  const { profile: user } = useSelector((state: RootState) => state.user);

  const handleTokenFromUrl = async () => {
    const token = tokenUtils.extractTokenFromUrl();
    if (!token) return;

    // Clear the token from URL
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.delete('authToken');
      window.history.replaceState({}, document.title, url.toString());
    }

    if (tokenUtils.isTokenExpired(token)) {
      dispatch(setError('Token has expired'));
      return;
    }

    // Verify token with backend and set in store
    const result = await dispatch(verifyAndSetToken(token));
    if (verifyAndSetToken.fulfilled.match(result)) {
      // Extract and set user profile from token
      const userProfile = tokenUtils.extractUserProfile(token);
      if (userProfile) {
        dispatch(setUserProfile({ ...userProfile}));
      }
    }
  };

  const logout = () => {
    dispatch(clearAuth());
    router.push('/');
  };

  return {
    ...auth,
    user,
    handleTokenFromUrl,
    logout,
  };
};