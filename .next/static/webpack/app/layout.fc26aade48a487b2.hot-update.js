"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8cae1fdf6224\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVXNlcnMvbW9oYW1tYWQvd29yay9zdXRyYS10aHJlYWRzLXVpL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4Y2FlMWZkZjYyMjRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(app-pages-browser)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var _store_slices_userSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/slices/userSlice */ \"(app-pages-browser)/./src/store/slices/userSlice.ts\");\n/* harmony import */ var _utils_token__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/token */ \"(app-pages-browser)/./src/utils/token.ts\");\n\n\n\n\n\nconst useAuth = ()=>{\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useDispatch)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const auth = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector)({\n        \"useAuth.useSelector[auth]\": (state)=>state.auth\n    }[\"useAuth.useSelector[auth]\"]);\n    const { profile: user } = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__.useSelector)({\n        \"useAuth.useSelector\": (state)=>state.user\n    }[\"useAuth.useSelector\"]);\n    const handleTokenFromUrl = async ()=>{\n        const token = _utils_token__WEBPACK_IMPORTED_MODULE_3__.tokenUtils.extractTokenFromUrl();\n        if (!token) return;\n        // Clear the token from URL\n        if (true) {\n            const url = new URL(window.location.href);\n            url.searchParams.delete('authToken');\n            window.history.replaceState({}, document.title, url.toString());\n        }\n        if (_utils_token__WEBPACK_IMPORTED_MODULE_3__.tokenUtils.isTokenExpired(token)) {\n            dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_1__.setError)('Token has expired'));\n            return;\n        }\n        // Verify token with backend and set in store\n        const result = await dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_1__.verifyAndSetToken)(token));\n        if (_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_1__.verifyAndSetToken.fulfilled.match(result)) {\n            // Extract and set user profile from token\n            const userProfile = _utils_token__WEBPACK_IMPORTED_MODULE_3__.tokenUtils.extractUserProfile(token);\n            if (userProfile) {\n                dispatch((0,_store_slices_userSlice__WEBPACK_IMPORTED_MODULE_2__.setUserProfile)({\n                    ...userProfile\n                }));\n            }\n        }\n    };\n    const logout = ()=>{\n        dispatch((0,_store_slices_authSlice__WEBPACK_IMPORTED_MODULE_1__.clearAuth)());\n        router.push('/');\n    };\n    return {\n        ...auth,\n        user,\n        handleTokenFromUrl,\n        logout\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});