"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_DarkModeToggle_DarkModeToggle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/DarkModeToggle/DarkModeToggle */ \"(app-pages-browser)/./src/components/ui/DarkModeToggle/DarkModeToggle.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.ts\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const { isAuthenticated, loading: isLoading, error } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const { profile } = (0,react_redux__WEBPACK_IMPORTED_MODULE_3__.useSelector)({\n        \"Home.useSelector\": (state)=>state.user\n    }[\"Home.useSelector\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col items-center justify-center p-4\",\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-500\",\n                    children: \"Checking authentication status...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true) : isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 dark:text-white text-gray-500\",\n                    children: [\n                        \"Welcome back! \",\n                        profile.name,\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DarkModeToggle_DarkModeToggle__WEBPACK_IMPORTED_MODULE_1__.DarkModeToggle, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6\",\n                    children: \"Welcome to the Application\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-red-100 text-red-700 rounded-md\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"Please sign in to access the application.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n            lineNumber: 25,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/work/sutra-threads-ui/src/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"5QVNbLUnUAB78sqsYviupyQ3GOU=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        react_redux__WEBPACK_IMPORTED_MODULE_3__.useSelector\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});