"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/slices/userSlice.ts":
/*!***************************************!*\
  !*** ./src/store/slices/userSlice.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearUserProfile: () => (/* binding */ clearUserProfile),\n/* harmony export */   setUserError: () => (/* binding */ setUserError),\n/* harmony export */   setUserProfile: () => (/* binding */ setUserProfile),\n/* harmony export */   userReducer: () => (/* binding */ userReducer)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n\n// Define the initial state\nconst initialState = {\n    profile: null,\n    loading: false,\n    error: null\n};\n// Create the user slice\nconst userSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'user',\n    initialState,\n    reducers: {\n        setUserProfile: (state, action)=>{\n            if (state.profile) {\n                // Update existing profile with new data\n                state.profile = {\n                    ...state.profile,\n                    ...action.payload\n                };\n            } else if ('id' in action.payload) {\n                // Only set as full profile if it has an ID\n                state.profile = action.payload;\n            } else {\n                // Otherwise, set as partial profile\n                state.profile = action.payload;\n            }\n        },\n        clearUserProfile: (state)=>{\n            state.profile = null;\n        },\n        setUserError: (state, action)=>{\n            state.error = action.payload;\n        }\n    }\n});\n// Export actions and reducer\nconst { setUserProfile, clearUserProfile, setUserError } = userSlice.actions;\nconst userReducer = userSlice.reducer;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/slices/userSlice.ts\n"));

/***/ })

});