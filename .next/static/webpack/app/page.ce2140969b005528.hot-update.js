"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/DarkModeToggle/DarkModeToggle.tsx":
/*!*************************************************************!*\
  !*** ./src/components/ui/DarkModeToggle/DarkModeToggle.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DarkModeToggle: () => (/* binding */ DarkModeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\n\nconst DarkModeToggle = ()=>{\n    _s();\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DarkModeToggle.useEffect\": ()=>{\n            const html = document.querySelector('html');\n            setIsDark((html === null || html === void 0 ? void 0 : html.dataset.theme) === 'dark');\n        }\n    }[\"DarkModeToggle.useEffect\"], []);\n    const handleToggle = ()=>{\n        const html = document.querySelector('html');\n        const newIsDark = !isDark;\n        setIsDark(newIsDark);\n        if (html) {\n            if (newIsDark) {\n                html.setAttribute('data-theme', 'dark');\n            } else {\n                html.removeAttribute('data-theme');\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleToggle,\n        className: \"p-2 rounded-full hover:bg-gray-800 transition-colors duration-200 ease-in-out\",\n        \"aria-label\": \"Toggle dark mode\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-5 h-5\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 transition-all duration-300 ease-in-out transform \".concat(isDark ? 'opacity-0 rotate-90 scale-50' : 'opacity-100 rotate-0 scale-100', \" absolute top-0 left-0\")\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/components/ui/DarkModeToggle/DarkModeToggle.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 transition-all duration-300 ease-in-out transform \".concat(isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 rotate-90 scale-50', \" absolute top-0 left-0\")\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/work/sutra-threads-ui/src/components/ui/DarkModeToggle/DarkModeToggle.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/work/sutra-threads-ui/src/components/ui/DarkModeToggle/DarkModeToggle.tsx\",\n            lineNumber: 32,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/work/sutra-threads-ui/src/components/ui/DarkModeToggle/DarkModeToggle.tsx\",\n        lineNumber: 27,\n        columnNumber: 9\n    }, undefined);\n};\n_s(DarkModeToggle, \"q9ovQTvwIdpxeVii6kJLTuTYpwE=\");\n_c = DarkModeToggle;\nvar _c;\n$RefreshReg$(_c, \"DarkModeToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0RhcmtNb2RlVG9nZ2xlL0RhcmtNb2RlVG9nZ2xlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF5QztBQUNHO0FBRXJDLE1BQU1JLGlCQUFpQjs7SUFDMUIsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdILCtDQUFRQSxDQUFDO0lBRXJDRCxnREFBU0E7b0NBQUM7WUFDTixNQUFNSyxPQUFPQyxTQUFTQyxhQUFhLENBQUM7WUFDcENILFVBQVVDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTUcsT0FBTyxDQUFDQyxLQUFLLE1BQUs7UUFDdEM7bUNBQUcsRUFBRTtJQUVMLE1BQU1DLGVBQWU7UUFDakIsTUFBTUwsT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1FBQ3BDLE1BQU1JLFlBQVksQ0FBQ1I7UUFDbkJDLFVBQVVPO1FBRVYsSUFBSU4sTUFBTTtZQUNOLElBQUlNLFdBQVc7Z0JBQ1hOLEtBQUtPLFlBQVksQ0FBQyxjQUFjO1lBQ3BDLE9BQU87Z0JBQ0hQLEtBQUtRLGVBQWUsQ0FBQztZQUN6QjtRQUNKO0lBQ0o7SUFFQSxxQkFDSSw4REFBQ0M7UUFDR0MsU0FBU0w7UUFDVE0sV0FBVTtRQUNWQyxjQUFXO2tCQUVYLDRFQUFDQztZQUFJRixXQUFVOzs4QkFDWCw4REFBQ2pCLG9GQUFHQTtvQkFDQWlCLFdBQVcsNkRBRVYsT0FER2IsU0FBUyxpQ0FBaUMsa0NBQzdDOzs7Ozs7OEJBRUwsOERBQUNMLG9GQUFJQTtvQkFDRGtCLFdBQVcsNkRBRVYsT0FER2IsU0FBUyxtQ0FBbUMsZ0NBQy9DOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtyQixFQUFFO0dBMUNXRDtLQUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL21vaGFtbWFkL3dvcmsvc3V0cmEtdGhyZWFkcy11aS9zcmMvY29tcG9uZW50cy91aS9EYXJrTW9kZVRvZ2dsZS9EYXJrTW9kZVRvZ2dsZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTW9vbiwgU3VuIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBjb25zdCBEYXJrTW9kZVRvZ2dsZSA9ICgpID0+IHtcbiAgICBjb25zdCBbaXNEYXJrLCBzZXRJc0RhcmtdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgY29uc3QgaHRtbCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJ2h0bWwnKTtcbiAgICAgICAgc2V0SXNEYXJrKGh0bWw/LmRhdGFzZXQudGhlbWUgPT09ICdkYXJrJyk7XG4gICAgfSwgW10pO1xuXG4gICAgY29uc3QgaGFuZGxlVG9nZ2xlID0gKCkgPT4ge1xuICAgICAgICBjb25zdCBodG1sID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignaHRtbCcpO1xuICAgICAgICBjb25zdCBuZXdJc0RhcmsgPSAhaXNEYXJrO1xuICAgICAgICBzZXRJc0RhcmsobmV3SXNEYXJrKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChodG1sKSB7XG4gICAgICAgICAgICBpZiAobmV3SXNEYXJrKSB7XG4gICAgICAgICAgICAgICAgaHRtbC5zZXRBdHRyaWJ1dGUoJ2RhdGEtdGhlbWUnLCAnZGFyaycpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBodG1sLnJlbW92ZUF0dHJpYnV0ZSgnZGF0YS10aGVtZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcblxuICAgIHJldHVybiAoXG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVRvZ2dsZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS04MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGVhc2UtaW4tb3V0XCJcbiAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJUb2dnbGUgZGFyayBtb2RlXCJcbiAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTUgaC01XCI+XG4gICAgICAgICAgICAgICAgPFN1biBcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy01IGgtNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBpc0RhcmsgPyAnb3BhY2l0eS0wIHJvdGF0ZS05MCBzY2FsZS01MCcgOiAnb3BhY2l0eS0xMDAgcm90YXRlLTAgc2NhbGUtMTAwJ1xuICAgICAgICAgICAgICAgICAgICB9IGFic29sdXRlIHRvcC0wIGxlZnQtMGB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8TW9vbiBcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy01IGgtNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBpc0RhcmsgPyAnb3BhY2l0eS0xMDAgcm90YXRlLTAgc2NhbGUtMTAwJyA6ICdvcGFjaXR5LTAgcm90YXRlLTkwIHNjYWxlLTUwJ1xuICAgICAgICAgICAgICAgICAgICB9IGFic29sdXRlIHRvcC0wIGxlZnQtMGB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2J1dHRvbj5cbiAgICApO1xufTsiXSwibmFtZXMiOlsiTW9vbiIsIlN1biIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiRGFya01vZGVUb2dnbGUiLCJpc0RhcmsiLCJzZXRJc0RhcmsiLCJodG1sIiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwiZGF0YXNldCIsInRoZW1lIiwiaGFuZGxlVG9nZ2xlIiwibmV3SXNEYXJrIiwic2V0QXR0cmlidXRlIiwicmVtb3ZlQXR0cmlidXRlIiwiYnV0dG9uIiwib25DbGljayIsImNsYXNzTmFtZSIsImFyaWEtbGFiZWwiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DarkModeToggle/DarkModeToggle.tsx\n"));

/***/ })

});