"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@reduxjs";
exports.ids = ["vendor-chunks/@reduxjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReducerType: () => (/* binding */ ReducerType),\n/* harmony export */   SHOULD_AUTOBATCH: () => (/* binding */ SHOULD_AUTOBATCH),\n/* harmony export */   TaskAbortError: () => (/* binding */ TaskAbortError),\n/* harmony export */   Tuple: () => (/* binding */ Tuple),\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.__DO_NOT_USE__ActionTypes),\n/* harmony export */   addListener: () => (/* binding */ addListener),\n/* harmony export */   applyMiddleware: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware),\n/* harmony export */   asyncThunkCreator: () => (/* binding */ asyncThunkCreator),\n/* harmony export */   autoBatchEnhancer: () => (/* binding */ autoBatchEnhancer),\n/* harmony export */   bindActionCreators: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.bindActionCreators),\n/* harmony export */   buildCreateSlice: () => (/* binding */ buildCreateSlice),\n/* harmony export */   clearAllListeners: () => (/* binding */ clearAllListeners),\n/* harmony export */   combineReducers: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers),\n/* harmony export */   combineSlices: () => (/* binding */ combineSlices),\n/* harmony export */   compose: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.compose),\n/* harmony export */   configureStore: () => (/* binding */ configureStore),\n/* harmony export */   createAction: () => (/* binding */ createAction),\n/* harmony export */   createActionCreatorInvariantMiddleware: () => (/* binding */ createActionCreatorInvariantMiddleware),\n/* harmony export */   createAsyncThunk: () => (/* binding */ createAsyncThunk),\n/* harmony export */   createDraftSafeSelector: () => (/* binding */ createDraftSafeSelector),\n/* harmony export */   createDraftSafeSelectorCreator: () => (/* binding */ createDraftSafeSelectorCreator),\n/* harmony export */   createDynamicMiddleware: () => (/* binding */ createDynamicMiddleware),\n/* harmony export */   createEntityAdapter: () => (/* binding */ createEntityAdapter),\n/* harmony export */   createImmutableStateInvariantMiddleware: () => (/* binding */ createImmutableStateInvariantMiddleware),\n/* harmony export */   createListenerMiddleware: () => (/* binding */ createListenerMiddleware),\n/* harmony export */   createNextState: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.produce),\n/* harmony export */   createReducer: () => (/* binding */ createReducer),\n/* harmony export */   createSelector: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.createSelector),\n/* harmony export */   createSelectorCreator: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.createSelectorCreator),\n/* harmony export */   createSerializableStateInvariantMiddleware: () => (/* binding */ createSerializableStateInvariantMiddleware),\n/* harmony export */   createSlice: () => (/* binding */ createSlice),\n/* harmony export */   createStore: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.createStore),\n/* harmony export */   current: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.current),\n/* harmony export */   findNonSerializableValue: () => (/* binding */ findNonSerializableValue),\n/* harmony export */   formatProdErrorMessage: () => (/* binding */ formatProdErrorMessage),\n/* harmony export */   freeze: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.freeze),\n/* harmony export */   isAction: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.isAction),\n/* harmony export */   isActionCreator: () => (/* binding */ isActionCreator),\n/* harmony export */   isAllOf: () => (/* binding */ isAllOf),\n/* harmony export */   isAnyOf: () => (/* binding */ isAnyOf),\n/* harmony export */   isAsyncThunkAction: () => (/* binding */ isAsyncThunkAction),\n/* harmony export */   isDraft: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.isDraft),\n/* harmony export */   isFluxStandardAction: () => (/* binding */ isFSA),\n/* harmony export */   isFulfilled: () => (/* binding */ isFulfilled),\n/* harmony export */   isImmutableDefault: () => (/* binding */ isImmutableDefault),\n/* harmony export */   isPending: () => (/* binding */ isPending),\n/* harmony export */   isPlain: () => (/* binding */ isPlain),\n/* harmony export */   isPlainObject: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject),\n/* harmony export */   isRejected: () => (/* binding */ isRejected),\n/* harmony export */   isRejectedWithValue: () => (/* binding */ isRejectedWithValue),\n/* harmony export */   legacy_createStore: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.legacy_createStore),\n/* harmony export */   lruMemoize: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.lruMemoize),\n/* harmony export */   miniSerializeError: () => (/* binding */ miniSerializeError),\n/* harmony export */   nanoid: () => (/* binding */ nanoid),\n/* harmony export */   original: () => (/* reexport safe */ immer__WEBPACK_IMPORTED_MODULE_2__.original),\n/* harmony export */   prepareAutoBatched: () => (/* binding */ prepareAutoBatched),\n/* harmony export */   removeListener: () => (/* binding */ removeListener),\n/* harmony export */   unwrapResult: () => (/* binding */ unwrapResult),\n/* harmony export */   weakMapMemoize: () => (/* reexport safe */ reselect__WEBPACK_IMPORTED_MODULE_1__.weakMapMemoize)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! immer */ \"(ssr)/./node_modules/immer/dist/immer.mjs\");\n/* harmony import */ var reselect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reselect */ \"(ssr)/./node_modules/reselect/dist/reselect.mjs\");\n/* harmony import */ var redux_thunk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-thunk */ \"(ssr)/./node_modules/redux-thunk/dist/redux-thunk.mjs\");\n// src/index.ts\n\n\n\n\n// src/createDraftSafeSelector.ts\n\n\nvar createDraftSafeSelectorCreator = (...args) => {\n  const createSelector2 = (0,reselect__WEBPACK_IMPORTED_MODULE_1__.createSelectorCreator)(...args);\n  const createDraftSafeSelector2 = Object.assign((...args2) => {\n    const selector = createSelector2(...args2);\n    const wrappedSelector = (value, ...rest) => selector((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.current)(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector;\n  }, {\n    withTypes: () => createDraftSafeSelector2\n  });\n  return createDraftSafeSelector2;\n};\nvar createDraftSafeSelector = /* @__PURE__ */ createDraftSafeSelectorCreator(reselect__WEBPACK_IMPORTED_MODULE_1__.weakMapMemoize);\n\n// src/configureStore.ts\n\n\n// src/devtoolsExtension.ts\n\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function() {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return redux__WEBPACK_IMPORTED_MODULE_0__.compose;\n  return redux__WEBPACK_IMPORTED_MODULE_0__.compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function() {\n  return function(noop3) {\n    return noop3;\n  };\n};\n\n// src/getDefaultMiddleware.ts\n\n\n// src/createAction.ts\n\n\n// src/tsHelpers.ts\nvar hasMatchFunction = (v) => {\n  return v && typeof v.match === \"function\";\n};\n\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator(...args) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error( false ? 0 : \"prepareAction did not return an object\");\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...\"meta\" in prepared && {\n          meta: prepared.meta\n        },\n        ...\"error\" in prepared && {\n          error: prepared.error\n        }\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action) => (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && action.type === type;\n  return actionCreator;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action && // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\n\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  const splitType = type ? `${type}`.split(\"/\") : [];\n  const actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return `Detected an action creator with type \"${type || \"unknown\"}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nfunction createActionCreatorInvariantMiddleware(options = {}) {\n  if (false) {}\n  const {\n    isActionCreator: isActionCreator2 = isActionCreator\n  } = options;\n  return () => (next) => (action) => {\n    if (isActionCreator2(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}\n\n// src/utils.ts\n\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  let elapsed = 0;\n  return {\n    measureTime(fn) {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nvar Tuple = class _Tuple extends Array {\n  constructor(...items) {\n    super(...items);\n    Object.setPrototypeOf(this, _Tuple.prototype);\n  }\n  static get [Symbol.species]() {\n    return _Tuple;\n  }\n  concat(...arr) {\n    return super.concat.apply(this, arr);\n  }\n  prepend(...arr) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new _Tuple(...arr[0].concat(this));\n    }\n    return new _Tuple(...arr.concat(this));\n  }\n};\nfunction freezeDraftable(val) {\n  return (0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(val) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(val, () => {\n  }) : val;\n}\nfunction getOrInsertComputed(map, key, compute) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, compute(key)).get(key);\n}\n\n// src/immutableStateInvariantMiddleware.ts\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable, ignorePaths = [], obj, path = \"\", checkedObjects = /* @__PURE__ */ new Set()) {\n  const tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable, ignoredPaths = [], trackedProperty, obj, sameParentRef = false, path = \"\") {\n  const prevObj = trackedProperty ? trackedProperty.value : void 0;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  const keysToDetect = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    let stringify2 = function(obj, serializer, indent, decycler) {\n      return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);\n    }, getSerialize2 = function(serializer, decycler) {\n      let stack = [], keys = [];\n      if (!decycler) decycler = function(_, value) {\n        if (stack[0] === value) return \"[Circular ~]\";\n        return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n      };\n      return function(key, value) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    };\n    var stringify = stringify2, getSerialize = getSerialize2;\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return (next) => (action) => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected between dispatches, in the path '${result.path || \"\"}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error( false ? 0 : `A state mutation was detected inside a dispatch, in the path: ${result.path || \"\"}. Take a look at the reducer(s) handling the action ${stringify2(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}\n\n// src/serializableStateInvariantMiddleware.ts\n\nfunction isPlain(val) {\n  const type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || (0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(val);\n}\nfunction findNonSerializableValue(value, path = \"\", isSerializable = isPlain, getEntries, ignoredPaths = [], cache) {\n  let foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware(options = {}) {\n  if (false) {} else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = [\"meta.arg\", \"meta.baseQueryMeta\"],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache = !disableCache && WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;\n    return (storeAPI) => (next) => (action) => {\n      if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}\n\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nvar buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(redux_thunk__WEBPACK_IMPORTED_MODULE_3__.thunk);\n    } else {\n      middlewareArray.push((0,redux_thunk__WEBPACK_IMPORTED_MODULE_3__.withExtraArgument)(thunk.extraArgument));\n    }\n  }\n  if (true) {\n    if (immutableCheck) {\n      let immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      let serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n};\n\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = () => (payload) => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nvar createQueueWithTimer = (timeout) => {\n  return (notify) => {\n    setTimeout(notify, timeout);\n  };\n};\nvar autoBatchEnhancer = (options = {\n  type: \"raf\"\n}) => (next) => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = /* @__PURE__ */ new Set();\n  const queueCallback = options.type === \"tick\" ? queueMicrotask : options.type === \"raf\" ? (\n    // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n    typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10)\n  ) : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach((l) => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener2) {\n      const wrappedListener = () => notifying && listener2();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener2);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener2);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action) {\n      try {\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        return store.dispatch(action);\n      } finally {\n        notifying = true;\n      }\n    }\n  });\n};\n\n// src/getDefaultEnhancers.ts\nvar buildGetDefaultEnhancers = (middlewareEnhancer) => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === \"object\" ? autoBatch : void 0));\n  }\n  return enhancerArray;\n};\n\n// src/configureStore.ts\nfunction configureStore(options) {\n  const getDefaultMiddleware = buildGetDefaultMiddleware();\n  const {\n    reducer = void 0,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = void 0,\n    enhancers = void 0\n  } = options || {};\n  let rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if ((0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(reducer)) {\n    rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducer);\n  } else {\n    throw new Error( false ? 0 : \"`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers\");\n  }\n  if ( true && middleware && typeof middleware !== \"function\") {\n    throw new Error( false ? 0 : \"`middleware` field must be a callback\");\n  }\n  let finalMiddleware;\n  if (typeof middleware === \"function\") {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if ( true && !Array.isArray(finalMiddleware)) {\n      throw new Error( false ? 0 : \"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if ( true && finalMiddleware.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each middleware provided to configureStore must be a function\");\n  }\n  if ( true && duplicateMiddlewareCheck) {\n    let middlewareReferences = /* @__PURE__ */ new Set();\n    finalMiddleware.forEach((middleware2) => {\n      if (middlewareReferences.has(middleware2)) {\n        throw new Error( false ? 0 : \"Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.\");\n      }\n      middlewareReferences.add(middleware2);\n    });\n  }\n  let finalCompose = redux__WEBPACK_IMPORTED_MODULE_0__.compose;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: \"development\" !== \"production\",\n      ...typeof devTools === \"object\" && devTools\n    });\n  }\n  const middlewareEnhancer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware)(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);\n  if ( true && enhancers && typeof enhancers !== \"function\") {\n    throw new Error( false ? 0 : \"`enhancers` field must be a callback\");\n  }\n  let storeEnhancers = typeof enhancers === \"function\" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if ( true && !Array.isArray(storeEnhancers)) {\n    throw new Error( false ? 0 : \"`enhancers` callback must return an array\");\n  }\n  if ( true && storeEnhancers.some((item) => typeof item !== \"function\")) {\n    throw new Error( false ? 0 : \"each enhancer provided to configureStore must be a function\");\n  }\n  if ( true && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error(\"middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`\");\n  }\n  const composedEnhancer = finalCompose(...storeEnhancers);\n  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.createStore)(rootReducer, preloadedState, composedEnhancer);\n}\n\n// src/createReducer.ts\n\n\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  const actionsMap = {};\n  const actionMatchers = [];\n  let defaultCaseReducer;\n  const builder = {\n    addCase(typeOrActionCreator, reducer) {\n      if (true) {\n        if (actionMatchers.length > 0) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error( false ? 0 : \"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error( false ? 0 : `\\`builder.addCase\\` cannot be called with two reducers for the same action type '${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher(matcher, reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer) {\n      if (true) {\n        if (defaultCaseReducer) {\n          throw new Error( false ? 0 : \"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nfunction createReducer(initialState, mapOrBuilderCallback) {\n  if (true) {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      throw new Error( false ? 0 : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n  let getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action) {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer: reducer2\n    }) => reducer2)];\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer) => {\n      if (caseReducer) {\n        if ((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(previousState)) {\n          const draft = previousState;\n          const result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!(0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(previousState)) {\n          const result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(previousState, (draft) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n\n// src/matchers.ts\nvar matches = (matcher, action) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf(...matchers) {\n  return (action) => {\n    return matchers.some((matcher) => matches(matcher, action));\n  };\n}\nfunction isAllOf(...matchers) {\n  return (action) => {\n    return matchers.every((matcher) => matches(matcher, action));\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === \"string\";\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.pending));\n}\nfunction isRejected(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.rejected));\n}\nfunction isRejectedWithValue(...asyncThunks) {\n  const hasFlag = (action) => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nfunction isFulfilled(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.fulfilled));\n}\nfunction isAsyncThunkAction(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap((asyncThunk) => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}\n\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = (size = 21) => {\n  let id = \"\";\n  let i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar FulfillWithMeta = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar miniSerializeError = (value) => {\n  if (typeof value === \"object\" && value !== null) {\n    const simpleError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar externalAbortMessage = \"External signal was aborted\";\nvar createAsyncThunk = /* @__PURE__ */ (() => {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    const fulfilled = createAction(typePrefix + \"/fulfilled\", (payload, requestId, arg, meta) => ({\n      payload,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"fulfilled\"\n      }\n    }));\n    const pending = createAction(typePrefix + \"/pending\", (requestId, arg, meta) => ({\n      payload: void 0,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"pending\"\n      }\n    }));\n    const rejected = createAction(typePrefix + \"/rejected\", (error, requestId, arg, payload, meta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: \"rejected\",\n        aborted: error?.name === \"AbortError\",\n        condition: error?.name === \"ConditionError\"\n      }\n    }));\n    function actionCreator(arg, {\n      signal\n    } = {}) {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler;\n        let abortReason;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener(\"abort\", () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function() {\n          let finalAction;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              throw {\n                name: \"ConditionError\",\n                message: \"Aborted due to condition callback returning false.\"\n              };\n            }\n            const abortedPromise = new Promise((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: \"AbortError\",\n                  message: abortReason || \"Aborted\"\n                });\n              };\n              abortController.signal.addEventListener(\"abort\", abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })));\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: (value, meta) => {\n                return new RejectWithValue(value, meta);\n              },\n              fulfillWithValue: (value, meta) => {\n                return new FulfillWithMeta(value, meta);\n              }\n            })).then((result) => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener(\"abort\", abortHandler);\n            }\n          }\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = () => createAsyncThunk2;\n  return createAsyncThunk2;\n})();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n\n// src/createSlice.ts\nvar asyncThunkSymbol = /* @__PURE__ */ Symbol.for(\"rtk-slice-createasyncthunk\");\nvar asyncThunkCreator = {\n  [asyncThunkSymbol]: createAsyncThunk\n};\nvar ReducerType = /* @__PURE__ */ ((ReducerType2) => {\n  ReducerType2[\"reducer\"] = \"reducer\";\n  ReducerType2[\"reducerWithPrepare\"] = \"reducerWithPrepare\";\n  ReducerType2[\"asyncThunk\"] = \"asyncThunk\";\n  return ReducerType2;\n})(ReducerType || {});\nfunction getType(slice, actionKey) {\n  return `${slice}/${actionKey}`;\n}\nfunction buildCreateSlice({\n  creators\n} = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice2(options) {\n    const {\n      name,\n      reducerPath = name\n    } = options;\n    if (!name) {\n      throw new Error( false ? 0 : \"`name` is a required option for createSlice\");\n    }\n    if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n      if (options.initialState === void 0) {\n        console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n      }\n    }\n    const reducers = (typeof options.reducers === \"function\" ? options.reducers(buildReducerCreators()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods = {\n      addCase(typeOrActionCreator, reducer2) {\n        const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with an empty action type\");\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error( false ? 0 : \"`context.addCase` cannot be called with two reducers for the same action type: \" + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer2;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer2) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer: reducer2\n        });\n        return contextMethods;\n      },\n      exposeAction(name2, actionCreator) {\n        context.actionCreators[name2] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name2, reducer2) {\n        context.sliceCaseReducersByName[name2] = reducer2;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach((reducerName) => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === \"function\"\n      };\n      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (true) {\n        if (typeof options.extraReducers === \"object\") {\n          throw new Error( false ? 0 : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, (builder) => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key]);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state) => state;\n    const injectedSelectorCache = /* @__PURE__ */ new Map();\n    const injectedStateCache = /* @__PURE__ */ new WeakMap();\n    let _reducer;\n    function reducer(state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps(reducerPath2, injected = false) {\n      function selectSlice(state) {\n        let sliceState = state[reducerPath2];\n        if (typeof sliceState === \"undefined\") {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (true) {\n            throw new Error( false ? 0 : \"selectSlice returned undefined for an uninjected slice reducer\");\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */ new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map = {};\n          for (const [name2, selector] of Object.entries(options.selectors ?? {})) {\n            map[name2] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        });\n      }\n      return {\n        reducerPath: reducerPath2,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice = {\n      name,\n      reducer,\n      actions: context.actionCreators,\n      caseReducers: context.sliceCaseReducersByName,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        };\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector(selector, selectState, getInitialState, injected) {\n  function wrapper(rootState, ...args) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === \"undefined\") {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (true) {\n        throw new Error( false ? 0 : \"selectState returned undefined for an uninjected slice reducer\");\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper;\n}\nvar createSlice = /* @__PURE__ */ buildCreateSlice();\nfunction buildReducerCreators() {\n  function asyncThunk(payloadCreator, config) {\n    return {\n      _reducerDefinitionType: \"asyncThunk\" /* asyncThunk */,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: \"reducer\" /* reducer */\n      });\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: \"reducerWithPrepare\" /* reducerWithPrepare */,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk\n  };\n}\nfunction handleNormalReducerDefinition({\n  type,\n  reducerName,\n  createNotation\n}, maybeReducerWithPrepare, context) {\n  let caseReducer;\n  let prepareCallback;\n  if (\"reducer\" in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error( false ? 0 : \"Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.\");\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"asyncThunk\" /* asyncThunk */;\n}\nfunction isCaseReducerWithPrepareDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"reducerWithPrepare\" /* reducerWithPrepare */;\n}\nfunction handleThunkCaseReducerDefinition({\n  type,\n  reducerName\n}, reducerDefinition, context, cAT) {\n  if (!cAT) {\n    throw new Error( false ? 0 : \"Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.\");\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {\n}\n\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory(stateAdapter) {\n  function getInitialState(additionalState = {}, entities) {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}\n\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState, options = {}) {\n    const {\n      createSelector: createSelector2 = createDraftSafeSelector\n    } = options;\n    const selectIds = (state) => state.ids;\n    const selectEntities = (state) => state.entities;\n    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map((id) => entities[id]));\n    const selectId = (_, id) => id;\n    const selectById = (entities, id) => entities[id];\n    const selectTotal = createSelector2(selectIds, (ids) => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector2(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);\n    return {\n      selectIds: createSelector2(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector2(selectState, selectAll),\n      selectTotal: createSelector2(selectState, selectTotal),\n      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}\n\n// src/entities/state_adapter.ts\n\nvar isDraftTyped = immer__WEBPACK_IMPORTED_MODULE_2__.isDraft;\nfunction createSingleArgumentStateOperator(mutator) {\n  const operator = createStateOperator((_, state) => mutator(state));\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    const runMutator = (draft) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped(state)) {\n      runMutator(state);\n      return state;\n    }\n    return (0,immer__WEBPACK_IMPORTED_MODULE_2__.produce)(state, runMutator);\n  };\n}\n\n// src/entities/utils.ts\n\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n  if ( true && key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction getCurrent(value) {\n  return (0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.current)(value) : value;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set(existingIdsArray);\n  const added = [];\n  const addedIds = /* @__PURE__ */ new Set([]);\n  const updated = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}\n\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    ;\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    let didMutate = false;\n    keys.forEach((key) => {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter((id) => id in state.entities);\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    const original3 = state.entities[update.id];\n    if (original3 === void 0) {\n      return false;\n    }\n    const updated = Object.assign({}, original3, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    ;\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    const updatesPerEntity = {};\n    updates.forEach((update) => {\n      if (update.id in state.entities) {\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter((update) => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map((e) => selectIdValue(e, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n\n// src/entities/sorted_state_adapter.ts\nfunction findInsertIndex(sortedItems, item, comparisonFunction) {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nfunction insert(sortedItems, item, comparisonFunction) {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nfunction createSortedStateAdapter(selectId, comparer) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state, existingIds) {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter((model) => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete state.entities[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        replacedIds = true;\n        delete state.entities[update.id];\n        const oldIndex = state.ids.indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities;\n    let ids = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n\n// src/entities/create_adapter.ts\nfunction createEntityAdapter(options = {}) {\n  const {\n    selectId,\n    sortComparer\n  } = {\n    sortComparer: false,\n    selectId: (instance) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}\n\n// src/listenerMiddleware/index.ts\n\n\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = `task-${cancelled}`;\nvar taskCompleted = `task-${completed}`;\nvar listenerCancelled = `${listener}-${cancelled}`;\nvar listenerCompleted = `${listener}-${completed}`;\nvar TaskAbortError = class {\n  constructor(code) {\n    this.code = code;\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n  name = \"TaskAbortError\";\n  message;\n};\n\n// src/listenerMiddleware/utils.ts\nvar assertFunction = (func, expected) => {\n  if (typeof func !== \"function\") {\n    throw new TypeError( false ? 0 : `${expected} is not a function`);\n  }\n};\nvar noop2 = () => {\n};\nvar catchRejection = (promise, onError = noop2) => {\n  promise.catch(onError);\n  return promise;\n};\nvar addAbortSignalListener = (abortSignal, callback) => {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener(\"abort\", callback);\n};\nvar abortControllerWithReason = (abortController, reason) => {\n  const signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n\n// src/listenerMiddleware/task.ts\nvar validateActive = (signal) => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal;\n    throw new TaskAbortError(reason);\n  }\n};\nfunction raceWithSignal(signal, promise) {\n  let cleanup = noop2;\n  return new Promise((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    cleanup = noop2;\n  });\n}\nvar runTask = async (task2, cleanUp) => {\n  try {\n    await Promise.resolve();\n    const value = await task2();\n    return {\n      status: \"ok\",\n      value\n    };\n  } catch (error) {\n    return {\n      status: error instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\nvar createPause = (signal) => {\n  return (promise) => {\n    return catchRejection(raceWithSignal(signal, promise).then((output) => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = (signal) => {\n  const pause = createPause(signal);\n  return (timeoutMs) => {\n    return pause(new Promise((resolve) => setTimeout(resolve, timeoutMs)));\n  };\n};\n\n// src/listenerMiddleware/index.ts\nvar {\n  assign\n} = Object;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = (parentAbortSignal, parentBlockingPromises) => {\n  const linkControllers = (controller) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return (taskExecutor, opts) => {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask(async () => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result2 = await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      });\n      validateActive(childAbortController.signal);\n      return result2;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop2));\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = (startListening, signal) => {\n  const take = async (predicate, timeout) => {\n    validateActive(signal);\n    let unsubscribe = () => {\n    };\n    const tuplePromise = new Promise((resolve, reject) => {\n      let stopListening = startListening({\n        predicate,\n        effect: (action, listenerApi) => {\n          listenerApi.unsubscribe();\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise((resolve) => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      unsubscribe();\n    }\n  };\n  return (predicate, timeout) => catchRejection(take(predicate, timeout));\n};\nvar getListenerEntryPropsFrom = (options) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n  } else {\n    throw new Error( false ? 0 : \"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\nvar createListenerEntry = /* @__PURE__ */ assign((options) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: /* @__PURE__ */ new Set(),\n    unsubscribe: () => {\n      throw new Error( false ? 0 : \"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n});\nvar findListenerEntry = (listenerMap, options) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find((entry) => {\n    const matchPredicateOrType = typeof type === \"string\" ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nvar cancelActiveListeners = (entry) => {\n  entry.pending.forEach((controller) => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = (listenerMap) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/add`), {\n  withTypes: () => addListener\n});\nvar clearAllListeners = /* @__PURE__ */ createAction(`${alm}/removeAll`);\nvar removeListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n});\nvar defaultErrorHandler = (...args) => {\n  console.error(`${alm}/error`, ...args);\n};\nvar createListenerMiddleware = (middlewareOptions = {}) => {\n  const listenerMap = /* @__PURE__ */ new Map();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, \"onError\");\n  const insertEntry = (entry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options);\n    return insertEntry(entry);\n  };\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry, action, api, getOriginalState) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening, internalTaskController.signal);\n    const autoJoinPromises = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(\n        action,\n        // Use assign() rather than ... to avoid extra helper functions added to bundle\n        assign({}, api, {\n          getOriginalState,\n          condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),\n          take,\n          delay: createDelay(internalTaskController.signal),\n          pause: createPause(internalTaskController.signal),\n          extra,\n          signal: internalTaskController.signal,\n          fork: createFork(internalTaskController.signal, autoJoinPromises),\n          unsubscribe: entry.unsubscribe,\n          subscribe: () => {\n            listenerMap.set(entry.id, entry);\n          },\n          cancelActiveListeners: () => {\n            entry.pending.forEach((controller, _, set) => {\n              if (controller !== internalTaskController) {\n                abortControllerWithReason(controller, listenerCancelled);\n                set.delete(controller);\n              }\n            });\n          },\n          cancel: () => {\n            abortControllerWithReason(internalTaskController, listenerCancelled);\n            entry.pending.delete(internalTaskController);\n          },\n          throwIfCancelled: () => {\n            validateActive(internalTaskController.signal);\n          }\n        })\n      ));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: \"effect\"\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted);\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware = (api) => (next) => (action) => {\n    if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n    let originalState = api.getState();\n    const getOriginalState = () => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error( false ? 0 : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState;\n    };\n    let result;\n    try {\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: \"predicate\"\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n};\n\n// src/dynamicMiddleware/index.ts\n\nvar createMiddlewareEntry = (middleware) => ({\n  middleware,\n  applied: /* @__PURE__ */ new Map()\n});\nvar matchInstance = (instanceId) => (action) => action?.meta?.instanceId === instanceId;\nvar createDynamicMiddleware = () => {\n  const instanceId = nanoid();\n  const middlewareMap = /* @__PURE__ */ new Map();\n  const withMiddleware = Object.assign(createAction(\"dynamicMiddleware/add\", (...middlewares) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  });\n  const addMiddleware = Object.assign(function addMiddleware2(...middlewares) {\n    middlewares.forEach((middleware2) => {\n      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  });\n  const getFinalMiddleware = (api) => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map((entry) => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return (0,redux__WEBPACK_IMPORTED_MODULE_0__.compose)(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware = (api) => (next) => (action) => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};\n\n// src/combineSlices.ts\n\nvar isSliceLike = (maybeSliceLike) => \"reducerPath\" in maybeSliceLike && typeof maybeSliceLike.reducerPath === \"string\";\nvar getReducers = (slices) => slices.flatMap((sliceOrMap) => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));\nvar ORIGINAL_STATE = Symbol.for(\"rtk-state-proxy-original\");\nvar isStateProxy = (value) => !!value && !!value[ORIGINAL_STATE];\nvar stateProxyMap = /* @__PURE__ */ new WeakMap();\nvar createStateProxy = (state, reducerMap, initialStateCache) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === \"undefined\") {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== \"undefined\") return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        const reducerResult = reducer(void 0, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === \"undefined\") {\n          throw new Error( false ? 0 : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n}));\nvar original = (state) => {\n  if (!isStateProxy(state)) {\n    throw new Error( false ? 0 : \"original must be used on state Proxy\");\n  }\n  return state[ORIGINAL_STATE];\n};\nvar emptyObject = {};\nvar noopReducer = (state = emptyObject) => state;\nfunction combineSlices(...slices) {\n  const reducerMap = Object.fromEntries(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state, action) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache = {};\n  const inject = (slice, config = {}) => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector(selectorFn, selectState) {\n    return function selector2(state, ...args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  });\n}\n\n// src/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n\n//# sourceMappingURL=redux-toolkit.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\n");

/***/ })

};
;