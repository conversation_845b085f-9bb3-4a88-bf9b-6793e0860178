# Sutra-Threads
## A cohesive design system for building thoughtful digital experiences

Sutra-Threads is a comprehensive design system that empowers teams to create harmonious, accessible, and scalable interfaces. Drawing inspiration from its Sanskrit namesake meaning "thread," Sutra-Threads weaves together components, patterns, and principles that connect seamlessly across your applications.

### Core Principles:
- Coherence - Components that work together harmoniously, creating a unified experience
- Flexibility - Adaptable solutions that respond to different contexts and requirements
- Accessibility - Inclusive design that reaches all users regardless of ability
- Efficiency - Optimized workflows to accelerate the design and development process

### Philosophy
At Sutra.ai, we believe great design connects ideas, just as threads connect fabric. Sutra-Threads embodies this philosophy by creating connections between design decisions, development implementation, and user experience. Each component represents a small piece of wisdom that, when woven together, creates experiences that are both beautiful and meaningful.

---

## ✨ Key Features

🔐 **Complete Authentication System**
- JWT token-based authentication with automatic refresh
- URL token extraction and verification
- Protected routes and components
- User profile management with Redux

🏗️ **Modern Architecture**
- Next.js 15 with App Router
- TypeScript for type safety
- Redux Toolkit + Redux Persist for state management
- API proxy layer for secure backend communication

🎨 **Comprehensive UI Library**
- 30+ pre-built, accessible components
- Consistent design system with Tailwind CSS
- Responsive design with mobile-first approach
- Dark/light theme support ready

⚡ **Developer Experience**
- Hot reload and fast refresh
- ESLint + Prettier configuration
- Comprehensive TypeScript support
- Detailed documentation and examples

🚀 **Production Ready**
- Optimized build configuration
- Security headers and best practices
- Docker support for containerization
- CI/CD pipeline examples

## 🚀 Quick Start

```bash
# Clone the repository
git clone <your-repo-url>
cd sutra-threads-ui

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Start development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see your application.

## 📚 Documentation

| Document | Description |
|----------|-------------|
| **[Getting Started](./docs/GETTING_STARTED.md)** | Complete setup guide and project overview |
| **[Components](./docs/COMPONENTS.md)** | UI component library documentation |
| **[API Proxy](./docs/API_PROXY.md)** | Backend integration and proxy setup |
| **[State Management](./docs/STATE_MANAGEMENT.md)** | Redux setup and patterns |
| **[Contributing](./docs/CONTRIBUTING.md)** | Development guidelines and contribution process |
| **[Testing](./docs/TESTING.md)** | Testing strategies and examples |
| **[Deployment](./docs/DEPLOYMENT.md)** | Production deployment guide |
| **[Guidelines](./docs/GUIDELINES.md)** | Coding standards and conventions |

## 🏗️ Project Structure

```
sutra-threads-ui/
├── docs/                    # Comprehensive documentation
├── public/                  # Static assets
├── src/
│   ├── app/                # Next.js 15 App Router
│   │   ├── api/            # API routes (proxy layer)
│   │   └── ...             # Pages and layouts
│   ├── components/         # React components
│   │   ├── ui/             # UI component library
│   │   ├── auth/           # Authentication components
│   │   └── common/         # Shared components
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API services
│   ├── store/              # Redux store configuration
│   ├── types/              # TypeScript definitions
│   └── utils/              # Utility functions
├── scripts/                # Build and utility scripts
└── ...                     # Configuration files
```

## 🎨 UI Components

The template includes a comprehensive component library:

### Form Components
- **Button** - Multiple variants with loading states
- **CustomInput** - Advanced input with validation
- **Checkbox** - Styled checkboxes with labels
- **Select** - Dropdown selection component
- **Upload** - File upload with drag-and-drop

### Layout Components
- **PageHeader** - Consistent page headers with breadcrumbs
- **TopBar** - Application navigation
- **MainContent** - Responsive content wrapper

### Data Components
- **Table** - Advanced data table with sorting, filtering, pagination
- **EditableCell** - Inline editing capabilities
- **TableWithColumnManagement** - Column visibility management

### Feedback Components
- **Dialog** - Modal dialogs
- **Progress** - Loading indicators
- **Badge** - Status indicators
- **Tooltip** - Contextual help

[View complete component documentation →](./docs/COMPONENTS.md)

## 🔐 Authentication Flow

1. **Token Extraction** - Automatically extracts tokens from URL parameters
2. **Token Verification** - Validates tokens with your backend API
3. **State Management** - Stores authentication state in Redux with persistence
4. **Auto-Refresh** - Handles token refresh automatically
5. **Route Protection** - Protects routes based on authentication status

## 🔧 Configuration

### Environment Variables

```env
# Required: Your backend API URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:5001/api

# Optional: SSO login URL
NEXT_PUBLIC_SSO_LOGIN_URL=https://your-sso-provider.com/login

# Required: Your application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Backend Integration

The template uses a proxy pattern where:
- Frontend calls Next.js API routes (`/api/*`)
- API routes proxy requests to your backend
- Authentication tokens are handled server-side

[Learn more about API proxy setup →](./docs/API_PROXY.md)

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **State Management**: Redux Toolkit + Redux Persist
- **UI Components**: Radix UI primitives
- **HTTP Client**: Axios
- **Icons**: Heroicons, Lucide React
- **Forms**: React Hook Form + Yup validation

## 🚦 Getting Started

1. **[Read the Getting Started Guide](./docs/GETTING_STARTED.md)** - Complete setup instructions
2. **[Explore the Components](./docs/COMPONENTS.md)** - Learn about available UI components
3. **[Set up Backend Integration](./docs/API_PROXY.md)** - Connect to your API
4. **[Configure Authentication](./docs/STATE_MANAGEMENT.md)** - Customize auth flow
5. **[Deploy to Production](./docs/DEPLOYMENT.md)** - Launch your application

## 🤝 Contributing

We welcome contributions! Please read our [Contributing Guide](./docs/CONTRIBUTING.md) to get started.

## 🆘 Support

- **Documentation**: Check the [docs folder](./docs/) for detailed guides
- **Issues**: Report bugs or request features via GitHub Issues
- **Examples**: Look at the demo pages for implementation examples

---

**Ready to build something amazing?** Start with the [Getting Started Guide](./docs/GETTING_STARTED.md) and explore the comprehensive documentation to make the most of this template.

