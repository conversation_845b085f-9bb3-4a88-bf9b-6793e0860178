# Getting Started with Sutra-Threads UI Template

Welcome to **Sutra-Threads**, a production-ready Next.js 15 template designed to accelerate your application development. This template provides a comprehensive foundation with built-in authentication, state management, API services, and a complete UI component library.

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18.17 or later
- **npm**, **yarn**, or **pnpm** package manager
- **Git** for version control

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd sutra-threads-ui
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables in `.env.local`:
   ```env
   # External API URL (your backend API)
   NEXT_PUBLIC_API_BASE_URL=http://localhost:5001/api

   # SSO Login URL (if using external authentication)
   NEXT_PUBLIC_SSO_LOGIN_URL=https://your-auth-provider.com/login

   # App URL (your frontend URL)
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see your application.

## 📁 Project Structure

```
sutra-threads-ui/
├── docs/                          # Documentation
│   ├── GETTING_STARTED.md         # This file
│   ├── API_PROXY.md              # API proxy documentation
│   ├── CONTRIBUTING.md           # Contribution guidelines
│   └── GUIDELINES.md             # Coding standards
├── public/                        # Static assets
│   ├── icons/                    # Icon files
│   └── images/                   # Image assets
├── src/
│   ├── app/                      # Next.js 15 App Router
│   │   ├── api/                  # API routes (proxy layer)
│   │   ├── dashboard/            # Dashboard pages
│   │   ├── table-demo/           # Table component demo
│   │   ├── layout.tsx            # Root layout
│   │   └── page.tsx              # Home page
│   ├── components/               # React components
│   │   ├── auth/                 # Authentication components
│   │   ├── common/               # Shared components
│   │   ├── layout/               # Layout components
│   │   └── ui/                   # UI component library
│   ├── constants/                # Application constants
│   ├── hooks/                    # Custom React hooks
│   ├── lib/                      # Utility libraries
│   ├── services/                 # API services
│   ├── store/                    # Redux store configuration
│   ├── styles/                   # Global styles
│   ├── types/                    # TypeScript type definitions
│   └── utils/                    # Utility functions
├── scripts/                      # Build and utility scripts
├── package.json                  # Dependencies and scripts
├── next.config.ts               # Next.js configuration
├── tailwind.config.ts           # Tailwind CSS configuration
└── tsconfig.json                # TypeScript configuration
```

## 🏗️ Architecture Overview

### Core Technologies

- **Next.js 15** - React framework with App Router
- **React 19** - UI library with latest features
- **TypeScript** - Type-safe development
- **Redux Toolkit** - State management
- **Redux Persist** - State persistence
- **Tailwind CSS 4** - Utility-first styling
- **Radix UI** - Accessible component primitives
- **Axios** - HTTP client for API calls

### Key Features

✅ **Authentication System**
- Token-based authentication with JWT
- Automatic token verification and refresh
- Protected routes and components
- User profile management

✅ **API Proxy Layer**
- Next.js API routes as proxy to backend
- Centralized error handling
- Token management on server-side
- CORS and security handling

✅ **State Management**
- Redux Toolkit for predictable state updates
- Redux Persist for data persistence
- Organized slices for different domains
- TypeScript integration

✅ **UI Component Library**
- 30+ pre-built components
- Consistent design system
- Accessibility built-in
- Customizable themes

✅ **Developer Experience**
- ESLint and Prettier configuration
- TypeScript strict mode
- Hot reload and fast refresh
- Comprehensive documentation

## 🔐 Authentication Flow

The template includes a complete authentication system:

1. **Token Extraction**: Automatically extracts tokens from URL parameters
2. **Token Verification**: Validates tokens with your backend API
3. **State Management**: Stores authentication state in Redux
4. **Auto-Refresh**: Handles token refresh automatically
5. **Route Protection**: Protects routes based on authentication status

### Authentication Components

- `TokenHandler` - Handles token extraction from URL
- `useAuth` hook - Provides authentication utilities
- `authService` - API calls for authentication
- `authSlice` - Redux state management for auth

## 🎨 UI Components

The template includes a comprehensive UI library with these components:

### Form Components
- `Button` - Customizable buttons with variants and loading states
- `CustomInput` - Advanced input with validation and info tooltips
- `Checkbox` & `CheckboxWithLabel` - Styled checkboxes
- `Select` - Dropdown selection component
- `Upload` - File upload with drag-and-drop

### Layout Components
- `PageHeader` - Consistent page headers with breadcrumbs
- `TopBar` - Application top navigation
- `MainContent` - Main content wrapper with responsive layout

### Data Components
- `Table` - Advanced data table with sorting, filtering, pagination
- `EditableCell` - Inline editing capabilities
- `TableWithColumnManagement` - Column visibility management

### Feedback Components
- `Dialog` - Modal dialogs
- `Progress` & `ProgressBar` - Loading indicators
- `Badge` & `Tag` - Status indicators
- `Tooltip` - Contextual help

### Navigation Components
- `Tabs` - Tab navigation
- `DropdownMenu` - Context menus
- `Popover` - Floating content

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file with these variables:

```env
# Required: Your backend API URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:5001/api

# Optional: SSO login URL for external authentication
NEXT_PUBLIC_SSO_LOGIN_URL=https://your-sso-provider.com/login

# Required: Your application URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### API Configuration

The template uses a proxy pattern where:
- Frontend calls Next.js API routes (`/api/*`)
- API routes proxy requests to your backend
- Authentication tokens are handled server-side

Configure your backend endpoints in `src/constants/endpoints.ts`:

```typescript
export const EXTERNAL_ENDPOINTS = {
  AUTH: {
    VERIFY: '/auth/verify',
    REFRESH: '/auth/refresh',
  },
  // Add your endpoints here
} as const;
```

## 🚦 Next Steps

1. **[Read the API Proxy Documentation](./API_PROXY.md)** - Understand how to connect to your backend
2. **[Review the Component Library](../src/components/ui/)** - Explore available UI components
3. **[Check the Coding Guidelines](./GUIDELINES.md)** - Follow project conventions
4. **[Set up your Backend Integration](#backend-integration)** - Connect to your API

## 🔗 Backend Integration

To connect this template to your backend:

1. **Update API URLs** in `src/constants/api.ts`
2. **Configure Authentication Endpoints** in `src/constants/endpoints.ts`
3. **Customize Token Handling** in `src/utils/token.ts`
4. **Add API Services** in `src/services/`
5. **Update Type Definitions** in `src/types/`

Example service implementation:

```typescript
// src/services/userService.ts
import httpService from './http';
import { EXTERNAL_ENDPOINTS } from '@/constants/endpoints';

export const userService = {
  async getProfile(): Promise<UserProfile> {
    return httpService.get<UserProfile>(EXTERNAL_ENDPOINTS.USER.PROFILE);
  },

  async updateProfile(data: Partial<UserProfile>): Promise<UserProfile> {
    return httpService.put<UserProfile>(EXTERNAL_ENDPOINTS.USER.PROFILE, data);
  }
};
```

## 📚 Additional Resources

- **[Component Documentation](../src/components/ui/README.md)** - Detailed component usage
- **[State Management Guide](../src/store/README.md)** - Redux setup and patterns
- **[Styling Guide](../src/styles/README.md)** - Tailwind CSS customization
- **[Testing Guide](./TESTING.md)** - Testing strategies and examples

## 🆘 Getting Help

- **Issues**: Check existing issues or create a new one
- **Documentation**: Refer to the docs folder for detailed guides
- **Examples**: Look at the demo pages for implementation examples

---

**Ready to build something amazing?** Start by exploring the demo pages and customizing the components to match your needs!