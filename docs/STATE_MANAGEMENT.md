# State Management Documentation

This document explains the state management architecture using Redux Toolkit and Redux Persist in the Sutra-Threads template.

## 🏗️ Architecture Overview

The template uses a modern Redux setup with:

- **Redux Toolkit** - Simplified Redux with built-in best practices
- **Redux Persist** - Automatic state persistence to localStorage
- **TypeScript Integration** - Fully typed state and actions
- **Organized Slices** - Domain-specific state management
- **Middleware Support** - Custom middleware for enhanced functionality

## 📁 Store Structure

```
src/store/
├── index.ts              # Store configuration and setup
├── rootReducer.ts        # Combines all reducers
├── middleware.ts         # Custom middleware
├── types.ts             # TypeScript type definitions
└── slices/              # Individual state slices
    ├── authSlice.ts     # Authentication state
    ├── userSlice.ts     # User profile state
    ├── uiSlice.ts       # UI state (modals, loading, etc.)
    └── searchSlice.ts   # Search functionality state
```

## 🔧 Store Configuration

### Store Setup

The store is configured in `src/store/index.ts`:

```typescript
import { configureStore } from '@reduxjs/toolkit';
import { persistStore } from 'redux-persist';
import { rootReducer } from './rootReducer';
import { middleware } from './middleware';
import { setupHttpService } from '@/lib/setupHttp';

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware({
    serializableCheck: false, // Required for redux-persist
  }).concat(middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

const persistor = persistStore(store);

// Setup HTTP service with store access
setupHttpService(store);

export { store, persistor };
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### Root Reducer

The root reducer combines all slices with persistence configuration:

```typescript
import { combineReducers } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

// Import slices
import { authReducer } from './slices/authSlice';
import { userReducer } from './slices/userSlice';
import { uiReducer } from './slices/uiSlice';
import { searchReducer } from './slices/searchSlice';

// Configure persistence for auth
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['token', 'refreshToken', 'isAuthenticated'],
};

// Configure persistence for user
const userPersistConfig = {
  key: 'user',
  storage,
  whitelist: ['profile'],
};

export const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authReducer),
  user: persistReducer(userPersistConfig, userReducer),
  ui: uiReducer, // Not persisted
  search: searchReducer, // Not persisted
});
```

## 🔐 Authentication Slice

Manages authentication state and token handling:

```typescript
// src/store/slices/authSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authService } from '@/services/authService';

interface AuthState {
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

// Async thunk for token verification
export const verifyAndSetToken = createAsyncThunk(
  'auth/verifyAndSetToken',
  async (token: string, { rejectWithValue }) => {
    try {
      const isValid = await authService.verifyToken(token);
      if (!isValid) {
        return rejectWithValue('Invalid token');
      }
      return token;
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Token verification failed';
      return rejectWithValue(message);
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearAuth: (state) => {
      state.token = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
      state.error = null;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(verifyAndSetToken.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyAndSetToken.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(verifyAndSetToken.rejected, (state, action) => {
        state.loading = false;
        state.token = null;
        state.isAuthenticated = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearAuth, setError } = authSlice.actions;
export const authReducer = authSlice.reducer;
```

## 👤 User Slice

Manages user profile and related data:

```typescript
// src/store/slices/userSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserProfile } from '@/types/auth';

interface UserState {
  profile: UserProfile | null;
  loading: boolean;
  error: string | null;
}

const initialState: UserState = {
  profile: null,
  loading: false,
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserProfile: (state, action: PayloadAction<UserProfile>) => {
      state.profile = action.payload;
      state.error = null;
    },
    updateUserProfile: (state, action: PayloadAction<Partial<UserProfile>>) => {
      if (state.profile) {
        state.profile = { ...state.profile, ...action.payload };
      }
    },
    clearUserProfile: (state) => {
      state.profile = null;
      state.error = null;
    },
    setUserLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setUserError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setUserProfile,
  updateUserProfile,
  clearUserProfile,
  setUserLoading,
  setUserError,
} = userSlice.actions;

export const userReducer = userSlice.reducer;
```

## 🎨 UI Slice

Manages global UI state like modals, loading states, and notifications:

```typescript
// src/store/slices/uiSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  sidebarOpen: boolean;
  globalLoading: boolean;
  notifications: Notification[];
  modals: {
    [key: string]: boolean;
  };
}

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
}

const initialState: UIState = {
  sidebarOpen: false,
  globalLoading: false,
  notifications: [],
  modals: {},
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.globalLoading = action.payload;
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      );
    },
    openModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = true;
    },
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = false;
    },
  },
});

export const {
  toggleSidebar,
  setSidebarOpen,
  setGlobalLoading,
  addNotification,
  removeNotification,
  openModal,
  closeModal,
} = uiSlice.actions;

export const uiReducer = uiSlice.reducer;
```

## 🔍 Search Slice

Manages search functionality and results:

```typescript
// src/store/slices/searchSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SearchState {
  query: string;
  results: any[];
  loading: boolean;
  filters: {
    [key: string]: any;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: SearchState = {
  query: '',
  results: [],
  loading: false,
  filters: {},
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
};

const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.query = action.payload;
    },
    setSearchResults: (state, action: PayloadAction<any[]>) => {
      state.results = action.payload;
    },
    setSearchLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setSearchFilters: (state, action: PayloadAction<{ [key: string]: any }>) => {
      state.filters = action.payload;
    },
    updateSearchFilter: (state, action: PayloadAction<{ key: string; value: any }>) => {
      state.filters[action.payload.key] = action.payload.value;
    },
    setSearchPagination: (state, action: PayloadAction<Partial<SearchState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearSearch: (state) => {
      state.query = '';
      state.results = [];
      state.filters = {};
      state.pagination = initialState.pagination;
    },
  },
});

export const {
  setSearchQuery,
  setSearchResults,
  setSearchLoading,
  setSearchFilters,
  updateSearchFilter,
  setSearchPagination,
  clearSearch,
} = searchSlice.actions;

export const searchReducer = searchSlice.reducer;
```

## 🪝 Custom Hooks

### useAuth Hook

Provides authentication utilities and state:

```typescript
// src/hooks/useAuth.ts
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { AppDispatch, RootState } from '@/store/types';
import { verifyAndSetToken, clearAuth, setError } from '@/store/slices/authSlice';
import { setUserProfile } from '@/store/slices/userSlice';
import { tokenUtils } from '@/utils/token';

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const auth = useSelector((state: RootState) => state.auth);
  const { profile: user } = useSelector((state: RootState) => state.user);

  const handleTokenFromUrl = useCallback(async () => {
    const urlToken = tokenUtils.extractTokenFromUrl();
    if (urlToken) {
      try {
        await dispatch(verifyAndSetToken(urlToken)).unwrap();
        const userProfile = tokenUtils.extractUserProfile(urlToken);
        if (userProfile) {
          dispatch(setUserProfile(userProfile));
        }
        // Clean URL
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch (error) {
        console.error('Token verification failed:', error);
      }
    }
  }, [dispatch]);

  const logout = useCallback(() => {
    dispatch(clearAuth());
    router.push('/');
  }, [dispatch, router]);

  return {
    ...auth,
    user,
    handleTokenFromUrl,
    logout,
    isLoading: auth.loading,
    isAuthenticated: auth.isAuthenticated,
  };
};
```

### useAppSelector & useAppDispatch

Typed versions of Redux hooks:

```typescript
// src/store/types.ts
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import type { RootState, AppDispatch } from './index';

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
```

## 🔄 Redux Persist Configuration

### Persistence Setup

Redux Persist automatically saves and restores state:

```typescript
// Whitelist specific fields for persistence
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['token', 'refreshToken', 'isAuthenticated'], // Only persist these fields
};

// Blacklist sensitive data
const userPersistConfig = {
  key: 'user',
  storage,
  blacklist: ['loading', 'error'], // Don't persist these fields
};
```

### Provider Setup

Wrap your app with Redux and Persist providers:

```typescript
// src/lib/providers.tsx
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@/store';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <PersistGate loading={<div>Loading...</div>} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
}
```

## 🛠️ Best Practices

### 1. Slice Organization

- **One slice per domain** (auth, user, ui, etc.)
- **Keep slices focused** on a single responsibility
- **Use descriptive action names** with domain prefixes

### 2. State Shape

- **Normalize complex data** to avoid deep nesting
- **Separate loading states** for different operations
- **Include error handling** in each slice

### 3. Async Operations

- **Use createAsyncThunk** for API calls
- **Handle all states** (pending, fulfilled, rejected)
- **Provide meaningful error messages**

### 4. TypeScript Integration

- **Define interfaces** for all state shapes
- **Use typed hooks** (useAppSelector, useAppDispatch)
- **Export action types** for testing

### 5. Performance

- **Use selectors** to compute derived state
- **Memoize expensive selectors** with createSelector
- **Avoid unnecessary re-renders** with proper state structure

## 📊 Usage Examples

### Dispatching Actions

```typescript
import { useAppDispatch, useAppSelector } from '@/store/types';
import { setUserProfile, addNotification } from '@/store/slices';

function MyComponent() {
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.user.profile);

  const handleUpdateProfile = async (data: Partial<UserProfile>) => {
    try {
      dispatch(setUserProfile({ ...user, ...data }));
      dispatch(addNotification({
        type: 'success',
        message: 'Profile updated successfully'
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        message: 'Failed to update profile'
      }));
    }
  };

  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

### Creating Selectors

```typescript
// src/store/selectors.ts
import { createSelector } from '@reduxjs/toolkit';
import { RootState } from './types';

export const selectAuthenticatedUser = createSelector(
  [(state: RootState) => state.auth.isAuthenticated, (state: RootState) => state.user.profile],
  (isAuthenticated, profile) => isAuthenticated ? profile : null
);

export const selectSearchResultsWithFilters = createSelector(
  [(state: RootState) => state.search.results, (state: RootState) => state.search.filters],
  (results, filters) => {
    // Apply filters to results
    return results.filter(item => {
      // Filter logic here
      return true;
    });
  }
);
```

## 🧪 Testing Redux

### Testing Slices

```typescript
import { authReducer, clearAuth, verifyAndSetToken } from '@/store/slices/authSlice';

describe('authSlice', () => {
  it('should handle clearAuth', () => {
    const initialState = {
      token: 'some-token',
      isAuthenticated: true,
      loading: false,
      error: null,
    };

    const action = clearAuth();
    const state = authReducer(initialState, action);

    expect(state.token).toBeNull();
    expect(state.isAuthenticated).toBe(false);
  });
});
```

### Testing with Mock Store

```typescript
import { configureStore } from '@reduxjs/toolkit';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { rootReducer } from '@/store/rootReducer';

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState: initialState,
  });
};

const renderWithStore = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};
```

---

This state management setup provides a robust, scalable foundation for managing application state with excellent TypeScript support and automatic persistence.
