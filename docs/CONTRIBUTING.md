# Contributing to Sutra-Threads UI Template

Thank you for your interest in contributing to Sutra-Threads! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Types of Contributions

We welcome several types of contributions:

- **🐛 Bug Reports** - Help us identify and fix issues
- **✨ Feature Requests** - Suggest new features or improvements
- **📝 Documentation** - Improve or add documentation
- **🎨 UI Components** - Add new components or enhance existing ones
- **🔧 Code Improvements** - Refactor, optimize, or enhance existing code
- **🧪 Tests** - Add or improve test coverage

### Before You Start

1. **Check existing issues** to avoid duplicate work
2. **Review the [coding guidelines](./GUIDELINES.md)** to understand our standards
3. **Familiarize yourself** with the project structure and architecture
4. **Set up your development environment** following the [Getting Started guide](./GETTING_STARTED.md)

## 🚀 Development Setup

### Prerequisites

- Node.js 18.17 or later
- npm, yarn, or pnpm
- Git

### Setup Steps

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/your-username/sutra-threads-ui.git
   cd sutra-threads-ui
   ```

3. **Add upstream remote**:
   ```bash
   git remote add upstream https://github.com/original-repo/sutra-threads-ui.git
   ```

4. **Install dependencies**:
   ```bash
   npm install
   ```

5. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

6. **Start development server**:
   ```bash
   npm run dev
   ```

## 📋 Development Workflow

### 1. Planning Your Contribution

- **Create or comment on an issue** describing what you plan to work on
- **Get feedback** from maintainers before starting significant work
- **Break down large features** into smaller, manageable tasks

### 2. Making Changes

- **Follow the coding guidelines** outlined in [GUIDELINES.md](./GUIDELINES.md)
- **Write meaningful commit messages** using conventional commits format
- **Keep changes focused** - one feature or fix per pull request
- **Add tests** for new functionality
- **Update documentation** as needed

### 3. Testing Your Changes

```bash
# Run linting
npm run lint

# Run type checking
npm run type-check

# Run tests (when available)
npm run test

# Build the project
npm run build
```

### 4. Submitting Changes

1. **Commit your changes**:
   ```bash
   git add .
   git commit -m "feat: add new component for user profiles"
   ```

2. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

3. **Create a Pull Request** on GitHub with:
   - Clear title and description
   - Reference to related issues
   - Screenshots for UI changes
   - Testing instructions

## 🎨 Component Development Guidelines

### Creating New Components

1. **Create component directory** in `src/components/ui/`:
   ```
   src/components/ui/YourComponent/
   ├── YourComponent.tsx
   ├── YourComponent.types.ts
   ├── index.ts
   └── README.md (optional)
   ```

2. **Follow the component template**:
   ```typescript
   "use client";

   import React from 'react';
   import { YourComponentProps } from './YourComponent.types';

   export const YourComponent = ({
     // Props with defaults
     variant = 'primary',
     className = '',
     ...props
   }: YourComponentProps) => {
     // Component logic

     return (
       <div className={`base-classes ${className}`} {...props}>
         {/* Component content */}
       </div>
     );
   };
   ```

3. **Define TypeScript interfaces**:
   ```typescript
   // YourComponent.types.ts
   export interface YourComponentProps {
     variant?: 'primary' | 'secondary';
     className?: string;
     children?: React.ReactNode;
   }
   ```

4. **Export from index file**:
   ```typescript
   // index.ts
   export { YourComponent } from './YourComponent';
   export type { YourComponentProps } from './YourComponent.types';
   ```

5. **Add to main export**:
   ```typescript
   // src/components/ui/index.ts
   export * from "./YourComponent/YourComponent";
   ```

### Component Standards

- **Accessibility**: Use semantic HTML and ARIA attributes
- **Responsive Design**: Ensure components work on all screen sizes
- **TypeScript**: Provide comprehensive type definitions
- **Styling**: Use Tailwind CSS classes with consistent patterns
- **Props**: Support common props like `className`, `children`, etc.
- **Variants**: Provide different visual variants when appropriate

## 🔧 API Service Development

### Adding New Services

1. **Create service file** in `src/services/`:
   ```typescript
   // src/services/yourService.ts
   import httpService from './http';
   import { EXTERNAL_ENDPOINTS } from '@/constants/endpoints';

   export const yourService = {
     async getData(): Promise<YourDataType> {
       return httpService.get<YourDataType>(EXTERNAL_ENDPOINTS.YOUR.ENDPOINT);
     },

     async createData(data: CreateDataType): Promise<YourDataType> {
       return httpService.post<YourDataType>(EXTERNAL_ENDPOINTS.YOUR.ENDPOINT, data);
     }
   };
   ```

2. **Add endpoints** to `src/constants/endpoints.ts`:
   ```typescript
   export const EXTERNAL_ENDPOINTS = {
     // ... existing endpoints
     YOUR: {
       ENDPOINT: '/your/endpoint',
       CREATE: '/your/endpoint/create',
     },
   } as const;
   ```

3. **Create API proxy routes** in `src/app/api/`:
   ```typescript
   // src/app/api/your/endpoint/route.ts
   import { NextRequest } from 'next/server';
   import { proxyGet, proxyPost } from '@/lib/apiProxy';

   export async function GET(request: NextRequest) {
     return proxyGet(request, '/your/endpoint');
   }

   export async function POST(request: NextRequest) {
     return proxyPost(request, '/your/endpoint');
   }
   ```

## 🏪 State Management

### Adding New Redux Slices

1. **Create slice file** in `src/store/slices/`:
   ```typescript
   // src/store/slices/yourSlice.ts
   import { createSlice, PayloadAction } from '@reduxjs/toolkit';

   interface YourState {
     data: YourDataType[];
     loading: boolean;
     error: string | null;
   }

   const initialState: YourState = {
     data: [],
     loading: false,
     error: null,
   };

   const yourSlice = createSlice({
     name: 'your',
     initialState,
     reducers: {
       setData: (state, action: PayloadAction<YourDataType[]>) => {
         state.data = action.payload;
       },
       // ... other reducers
     },
   });

   export const { setData } = yourSlice.actions;
   export const yourReducer = yourSlice.reducer;
   ```

2. **Add to root reducer**:
   ```typescript
   // src/store/rootReducer.ts
   import { yourReducer } from './slices/yourSlice';

   export const rootReducer = combineReducers({
     // ... existing reducers
     your: yourReducer,
   });
   ```

## 📝 Documentation Standards

### Code Documentation

- **JSDoc comments** for complex functions
- **README files** for major features or components
- **Inline comments** for complex logic
- **Type definitions** with descriptive names

### Documentation Updates

When making changes, update relevant documentation:

- **Component README** files
- **API documentation** in docs/
- **Type definitions** and interfaces
- **Example usage** in demo pages

## 🧪 Testing Guidelines

### Testing Strategy

- **Unit Tests**: Test individual components and functions
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows
- **Visual Tests**: Test component appearance

### Writing Tests

```typescript
// Example component test
import { render, screen } from '@testing-library/react';
import { YourComponent } from './YourComponent';

describe('YourComponent', () => {
  it('renders correctly', () => {
    render(<YourComponent>Test content</YourComponent>);
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<YourComponent onClick={handleClick}>Click me</YourComponent>);

    screen.getByText('Click me').click();
    expect(handleClick).toHaveBeenCalled();
  });
});
```

## 🚨 Code Review Process

### Before Submitting

- [ ] Code follows project guidelines
- [ ] All tests pass
- [ ] Documentation is updated
- [ ] No console errors or warnings
- [ ] Responsive design works
- [ ] Accessibility requirements met

### Review Criteria

Reviewers will check for:

- **Code Quality**: Clean, readable, maintainable code
- **Performance**: Efficient implementations
- **Security**: No security vulnerabilities
- **Accessibility**: WCAG compliance
- **Testing**: Adequate test coverage
- **Documentation**: Clear and complete docs

## 🎯 Commit Message Format

We use [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples

```bash
feat(auth): add password reset functionality
fix(table): resolve sorting issue with dates
docs(api): update authentication flow documentation
style(button): improve hover state styling
refactor(utils): simplify token validation logic
test(components): add tests for Button component
chore(deps): update dependencies to latest versions
```

## 🏷️ Release Process

### Versioning

We follow [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Steps

1. **Update version** in `package.json`
2. **Update CHANGELOG.md** with new features and fixes
3. **Create release tag** and GitHub release
4. **Deploy to production** (if applicable)

## 🤔 Questions and Support

### Getting Help

- **GitHub Issues**: For bugs and feature requests
- **Discussions**: For questions and general discussion
- **Documentation**: Check existing docs first
- **Code Examples**: Look at demo pages and existing components

### Communication Guidelines

- **Be respectful** and constructive
- **Provide context** when asking questions
- **Include code examples** when reporting issues
- **Search existing issues** before creating new ones

## 📜 License

By contributing to Sutra-Threads, you agree that your contributions will be licensed under the same license as the project.

---

Thank you for contributing to Sutra-Threads! Your efforts help make this template better for everyone. 🙏