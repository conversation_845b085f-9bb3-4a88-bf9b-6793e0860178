# Testing Guide

This guide covers testing strategies, setup, and best practices for the Sutra-Threads template.

## 🧪 Testing Strategy

The template supports multiple testing approaches:

- **Unit Tests** - Test individual components and functions
- **Integration Tests** - Test component interactions and API calls
- **End-to-End Tests** - Test complete user workflows
- **Visual Regression Tests** - Test component appearance
- **Accessibility Tests** - Ensure WCAG compliance

## 🛠️ Testing Setup

### Install Testing Dependencies

```bash
npm install --save-dev \
  @testing-library/react \
  @testing-library/jest-dom \
  @testing-library/user-event \
  jest \
  jest-environment-jsdom \
  @types/jest
```

### Jest Configuration

Create `jest.config.js`:

```javascript
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    // Handle module aliases (this will be automatically configured for you based on your tsconfig.json paths)
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/app/layout.tsx',
    '!src/app/globals.css',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
```

### Jest Setup File

Create `jest.setup.js`:

```javascript
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Mock Redux store
jest.mock('@/store', () => ({
  store: {
    getState: jest.fn(() => ({
      auth: { isAuthenticated: false, token: null, loading: false, error: null },
      user: { profile: null, loading: false, error: null },
      ui: { sidebarOpen: false, globalLoading: false, notifications: [], modals: {} },
      search: { query: '', results: [], loading: false, filters: {}, pagination: { page: 1, limit: 10, total: 0 } },
    })),
    dispatch: jest.fn(),
    subscribe: jest.fn(),
  },
  persistor: {
    purge: jest.fn(),
    flush: jest.fn(),
    pause: jest.fn(),
    persist: jest.fn(),
  },
}));

// Mock environment variables
process.env.NEXT_PUBLIC_API_BASE_URL = 'http://localhost:5001/api';
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000';
```

## 🧩 Component Testing

### Basic Component Test

```typescript
// src/components/ui/Button/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button title="Test Button">Click me</Button>);
    
    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(
      <Button title="Test Button" onClick={handleClick}>
        Click me
      </Button>
    );

    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state', () => {
    render(
      <Button title="Test Button" loading loadingText="Loading...">
        Click me
      </Button>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('applies variant classes correctly', () => {
    render(
      <Button title="Test Button" variant="secondary">
        Secondary Button
      </Button>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-white', 'text-teal-500', 'border-2', 'border-teal-500');
  });
});
```

### Testing Components with Props

```typescript
// src/components/ui/CustomInput/CustomInput.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CustomInput } from './CustomInput';

describe('CustomInput Component', () => {
  const defaultProps = {
    label: 'Test Input',
    value: '',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with label', () => {
    render(<CustomInput {...defaultProps} />);
    
    expect(screen.getByLabelText('Test Input')).toBeInTheDocument();
  });

  it('handles input changes', async () => {
    const user = userEvent.setup();
    const handleChange = jest.fn();
    
    render(<CustomInput {...defaultProps} onChange={handleChange} />);
    
    const input = screen.getByLabelText('Test Input');
    await user.type(input, 'test value');
    
    expect(handleChange).toHaveBeenCalled();
  });

  it('shows error state', () => {
    render(
      <CustomInput
        {...defaultProps}
        error
        errorMessage="This field is required"
      />
    );

    expect(screen.getByText('This field is required')).toBeInTheDocument();
    expect(screen.getByLabelText('Test Input')).toHaveClass('border-red-500');
  });

  it('toggles password visibility', async () => {
    const user = userEvent.setup();
    
    render(<CustomInput {...defaultProps} type="password" />);
    
    const input = screen.getByLabelText('Test Input');
    const toggleButton = screen.getByRole('button');
    
    expect(input).toHaveAttribute('type', 'password');
    
    await user.click(toggleButton);
    expect(input).toHaveAttribute('type', 'text');
  });
});
```

## 🔗 Integration Testing

### Testing with Redux

```typescript
// src/components/auth/AuthComponent.test.tsx
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { rootReducer } from '@/store/rootReducer';
import { AuthComponent } from './AuthComponent';

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState: initialState,
  });
};

const renderWithStore = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('AuthComponent Integration', () => {
  it('shows login form when not authenticated', () => {
    const initialState = {
      auth: { isAuthenticated: false, token: null, loading: false, error: null },
      user: { profile: null, loading: false, error: null },
    };

    renderWithStore(<AuthComponent />, initialState);
    
    expect(screen.getByText('Please sign in')).toBeInTheDocument();
  });

  it('shows user profile when authenticated', () => {
    const initialState = {
      auth: { isAuthenticated: true, token: 'mock-token', loading: false, error: null },
      user: { 
        profile: { name: 'John Doe', email: '<EMAIL>' }, 
        loading: false, 
        error: null 
      },
    };

    renderWithStore(<AuthComponent />, initialState);
    
    expect(screen.getByText('Welcome back! John Doe')).toBeInTheDocument();
  });
});
```

### Testing API Calls

```typescript
// src/services/authService.test.ts
import { authService } from './authService';
import httpService from './http';

// Mock the http service
jest.mock('./http');
const mockedHttpService = httpService as jest.Mocked<typeof httpService>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyToken', () => {
    it('returns true for valid token', async () => {
      mockedHttpService.get.mockResolvedValue({ valid: true });

      const result = await authService.verifyToken('valid-token');

      expect(result).toBe(true);
      expect(mockedHttpService.get).toHaveBeenCalledWith(
        '/auth/verify',
        { params: { authToken: 'valid-token' } }
      );
    });

    it('returns false for invalid token', async () => {
      mockedHttpService.get.mockRejectedValue(new Error('Invalid token'));

      const result = await authService.verifyToken('invalid-token');

      expect(result).toBe(false);
    });
  });

  describe('getUserFromToken', () => {
    it('extracts user profile from valid token', () => {
      // Mock token utils
      const mockProfile = { name: 'John Doe', email: '<EMAIL>' };
      jest.spyOn(require('@/utils/token'), 'tokenUtils').mockReturnValue({
        extractUserProfile: jest.fn().mockReturnValue(mockProfile),
      });

      const result = authService.getUserFromToken('valid-token');

      expect(result).toEqual(mockProfile);
    });
  });
});
```

## 🎭 End-to-End Testing

### Playwright Setup

```bash
npm install --save-dev @playwright/test
npx playwright install
```

### Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Example

```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test('should handle token authentication', async ({ page }) => {
    // Navigate to app with token
    await page.goto('/?token=mock-valid-token');

    // Wait for authentication to complete
    await expect(page.locator('[data-testid="user-profile"]')).toBeVisible();

    // Check if user is authenticated
    await expect(page.locator('text=Welcome back!')).toBeVisible();
  });

  test('should show login prompt for unauthenticated users', async ({ page }) => {
    await page.goto('/');

    // Should show login prompt
    await expect(page.locator('text=Please sign in')).toBeVisible();
  });

  test('should handle logout', async ({ page }) => {
    // Start with authenticated state
    await page.goto('/?token=mock-valid-token');
    await expect(page.locator('[data-testid="user-profile"]')).toBeVisible();

    // Click logout button
    await page.click('[data-testid="logout-button"]');

    // Should redirect to login
    await expect(page.locator('text=Please sign in')).toBeVisible();
  });
});
```

## ♿ Accessibility Testing

### Jest-Axe Setup

```bash
npm install --save-dev jest-axe
```

### Accessibility Test Example

```typescript
// src/components/ui/Button/Button.a11y.test.tsx
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { Button } from './Button';

expect.extend(toHaveNoViolations);

describe('Button Accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(
      <Button title="Accessible Button">Click me</Button>
    );

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should have proper ARIA attributes', () => {
    const { getByRole } = render(
      <Button title="Test Button" loading loadingText="Loading...">
        Submit
      </Button>
    );

    const button = getByRole('button');
    expect(button).toHaveAttribute('aria-disabled', 'true');
    expect(button).toHaveAttribute('title', 'Test Button');
  });
});
```

## 📊 Visual Regression Testing

### Storybook Setup

```bash
npm install --save-dev @storybook/react @storybook/addon-essentials
```

### Component Story

```typescript
// src/components/ui/Button/Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'tertiary', 'ghost'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    title: 'Primary Button',
    variant: 'primary',
    children: 'Click me',
  },
};

export const Loading: Story = {
  args: {
    title: 'Loading Button',
    variant: 'primary',
    loading: true,
    loadingText: 'Loading...',
    children: 'Submit',
  },
};
```

## 🔧 Testing Utilities

### Custom Render Function

```typescript
// src/test-utils/render.tsx
import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { rootReducer } from '@/store/rootReducer';

interface ExtendedRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: any;
  store?: any;
}

export function renderWithProviders(
  ui: React.ReactElement,
  {
    preloadedState = {},
    store = configureStore({
      reducer: rootReducer,
      preloadedState,
    }),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  function Wrapper({ children }: { children?: React.ReactNode }) {
    return <Provider store={store}>{children}</Provider>;
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
}

// Re-export everything
export * from '@testing-library/react';
```

### Mock Data Factory

```typescript
// src/test-utils/factories.ts
export const createMockUser = (overrides = {}) => ({
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  avatar: 'https://example.com/avatar.jpg',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const createMockAuthState = (overrides = {}) => ({
  token: 'mock-token',
  refreshToken: 'mock-refresh-token',
  isAuthenticated: true,
  loading: false,
  error: null,
  ...overrides,
});

export const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState: {
      auth: createMockAuthState(),
      user: { profile: createMockUser(), loading: false, error: null },
      ui: { sidebarOpen: false, globalLoading: false, notifications: [], modals: {} },
      search: { query: '', results: [], loading: false, filters: {}, pagination: { page: 1, limit: 10, total: 0 } },
      ...initialState,
    },
  });
};
```

## 📋 Testing Scripts

Add these scripts to `package.json`:

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:a11y": "jest --testPathPattern=a11y",
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build"
  }
}
```

## 🎯 Testing Best Practices

### 1. Test Structure

- **Arrange**: Set up test data and conditions
- **Act**: Execute the code being tested
- **Assert**: Verify the expected outcome

### 2. Test Naming

```typescript
describe('Component/Feature Name', () => {
  it('should do something when condition is met', () => {
    // Test implementation
  });
});
```

### 3. Mock Strategy

- **Mock external dependencies** (APIs, third-party libraries)
- **Don't mock what you're testing**
- **Use realistic mock data**

### 4. Coverage Goals

- **Aim for 70-80% coverage** as a baseline
- **Focus on critical paths** and edge cases
- **Don't chase 100% coverage** at the expense of test quality

### 5. Test Maintenance

- **Keep tests simple** and focused
- **Update tests** when requirements change
- **Remove obsolete tests** regularly
- **Refactor tests** along with code

## 🚀 Continuous Integration

### GitHub Actions

```yaml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:coverage
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

---

This testing guide provides a comprehensive foundation for testing your Sutra-Threads application with modern tools and best practices.
