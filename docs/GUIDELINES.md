# Common Guidelines to follow in Sutra-Threads UI

## Naming Conventions
1. File Name convention --> Pascal case
2. Component Name convention --> Pascal case
3. Constant Name convention --> SCREAMING_SNAKE_CASE

## Extending

While building your on top of this library, you can extend the components to add your own customizations. For example, you can add your own custom button by extending the `Button` component.

Make sure to follow the same naming conventions and folder structure as the library.

Try to write down types for all the props you are using.

~Happy Coding!

