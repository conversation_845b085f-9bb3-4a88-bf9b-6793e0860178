# Architecture Documentation

This document provides a comprehensive overview of the Sutra-Threads template architecture, design patterns, and technical decisions.

## 🏗️ System Architecture

### High-Level Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Next.js API   │    │   Backend API   │
│   (React/Next)  │◄──►│   Proxy Layer   │◄──►│   (Your API)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Redux Store   │    │   Server HTTP   │    │   Database      │
│   (State Mgmt)  │    │   Client        │    │   (Your DB)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Architecture Layers

1. **Presentation Layer** - React components and UI
2. **State Management Layer** - Redux store and slices
3. **Service Layer** - API services and HTTP clients
4. **Proxy Layer** - Next.js API routes
5. **External Integration** - Backend API and services

## 🎯 Design Principles

### 1. Separation of Concerns

Each layer has a specific responsibility:
- **Components**: UI rendering and user interaction
- **Services**: API communication and data fetching
- **Store**: State management and business logic
- **Utils**: Pure functions and helpers

### 2. Dependency Injection

Services and utilities are injected rather than directly imported:

```typescript
// Good: Dependency injection
const httpService = new HttpService(API_BASE_URL);
httpService.setup({ tokenProvider, onAuthError });

// Avoid: Direct coupling
axios.defaults.baseURL = API_BASE_URL;
```

### 3. Type Safety

Everything is typed with TypeScript:
- Component props and state
- API request/response types
- Redux state and actions
- Service method signatures

### 4. Composition over Inheritance

Components are composed from smaller, reusable pieces:

```typescript
// Composed component
<Dialog>
  <DialogTrigger asChild>
    <Button>Open</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Title</DialogTitle>
    </DialogHeader>
  </DialogContent>
</Dialog>
```

## 🔄 Data Flow Architecture

### Request Flow

```
User Action → Component → Hook → Service → API Proxy → Backend API
     ↓           ↓         ↓        ↓         ↓           ↓
Response ← Component ← Hook ← Service ← API Proxy ← Backend API
     ↓
Redux Store Update
     ↓
Component Re-render
```

### Authentication Flow

```
1. Token in URL → TokenHandler extracts token
2. Token → authService.verifyToken() → API proxy → Backend
3. Valid token → Redux store update → User authenticated
4. Invalid token → Clear auth state → Show login prompt
```

### State Management Flow

```
Component Dispatch → Action Creator → Reducer → New State → Component Update
                                         ↓
                                   Redux Persist → localStorage
```

## 📁 Directory Structure

### Organized by Feature and Type

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API proxy routes
│   │   ├── auth/          # Authentication endpoints
│   │   ├── users/         # User management endpoints
│   │   └── health/        # Health check endpoint
│   ├── dashboard/         # Dashboard pages
│   ├── table-demo/        # Demo pages
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   │   ├── Button/       # Button component
│   │   ├── Table/        # Table components
│   │   └── ...           # Other UI components
│   ├── auth/             # Authentication components
│   ├── common/           # Shared components
│   └── layout/           # Layout components
├── hooks/                # Custom React hooks
│   ├── useAuth.ts        # Authentication hook
│   ├── useDebounce.ts    # Debounce hook
│   └── ...               # Other hooks
├── services/             # API services
│   ├── http.ts           # HTTP client
│   ├── authService.ts    # Authentication service
│   └── ...               # Other services
├── store/                # Redux store
│   ├── slices/           # Redux slices
│   │   ├── authSlice.ts  # Auth state
│   │   ├── userSlice.ts  # User state
│   │   └── ...           # Other slices
│   ├── index.ts          # Store configuration
│   └── types.ts          # Store types
├── types/                # TypeScript definitions
│   ├── auth.ts           # Auth types
│   ├── api.ts            # API types
│   └── ...               # Other types
├── utils/                # Utility functions
│   ├── token.ts          # Token utilities
│   ├── validation.ts     # Validation helpers
│   └── ...               # Other utilities
└── constants/            # Application constants
    ├── api.ts            # API URLs
    ├── endpoints.ts      # API endpoints
    └── ...               # Other constants
```

## 🔧 Component Architecture

### Component Hierarchy

```
App Layout
├── Providers (Redux, Theme)
├── TokenHandler (Auth)
├── TopBar (Navigation)
├── MainContent (Layout)
│   ├── PageHeader (Page-specific)
│   ├── Page Content
│   └── Modals/Dialogs
└── Global Components (Notifications, etc.)
```

### Component Patterns

#### 1. Container/Presentational Pattern

```typescript
// Container Component (logic)
const UserListContainer = () => {
  const users = useAppSelector(state => state.users.list);
  const dispatch = useAppDispatch();
  
  const handleDelete = (id: string) => {
    dispatch(deleteUser(id));
  };
  
  return <UserList users={users} onDelete={handleDelete} />;
};

// Presentational Component (UI)
const UserList = ({ users, onDelete }) => (
  <div>
    {users.map(user => (
      <UserCard key={user.id} user={user} onDelete={onDelete} />
    ))}
  </div>
);
```

#### 2. Compound Component Pattern

```typescript
// Compound component for complex UI
<Table>
  <TableHeader>
    <TableRow>
      <TableCell>Name</TableCell>
      <TableCell>Email</TableCell>
    </TableRow>
  </TableHeader>
  <TableBody>
    {data.map(item => (
      <TableRow key={item.id}>
        <TableCell>{item.name}</TableCell>
        <TableCell>{item.email}</TableCell>
      </TableRow>
    ))}
  </TableBody>
</Table>
```

#### 3. Render Props Pattern

```typescript
// Flexible data fetching component
<DataFetcher url="/api/users">
  {({ data, loading, error }) => (
    loading ? <Spinner /> :
    error ? <ErrorMessage error={error} /> :
    <UserList users={data} />
  )}
</DataFetcher>
```

## 🔐 Security Architecture

### Authentication Security

1. **Token Storage**: Tokens stored in Redux with persistence
2. **Token Validation**: Server-side validation on every request
3. **Automatic Refresh**: Transparent token refresh
4. **Secure Headers**: Security headers in API responses

### API Security

1. **Proxy Pattern**: Hide backend URLs from client
2. **Token Forwarding**: Server-side token handling
3. **Request Validation**: Input validation on proxy layer
4. **Error Sanitization**: Clean error responses

### Client Security

1. **CSP Headers**: Content Security Policy
2. **XSS Protection**: Input sanitization
3. **CSRF Protection**: Same-origin policy
4. **Secure Cookies**: HttpOnly and Secure flags

## 🚀 Performance Architecture

### Code Splitting

```typescript
// Route-based splitting
const Dashboard = lazy(() => import('./Dashboard'));
const UserManagement = lazy(() => import('./UserManagement'));

// Component-based splitting
const HeavyComponent = lazy(() => import('./HeavyComponent'));
```

### State Optimization

```typescript
// Memoized selectors
const selectFilteredUsers = createSelector(
  [selectUsers, selectFilters],
  (users, filters) => users.filter(user => matchesFilters(user, filters))
);

// Memoized components
const UserCard = memo(({ user, onDelete }) => (
  <div>
    <h3>{user.name}</h3>
    <button onClick={() => onDelete(user.id)}>Delete</button>
  </div>
));
```

### Bundle Optimization

1. **Tree Shaking**: Remove unused code
2. **Dynamic Imports**: Load code on demand
3. **Image Optimization**: Next.js Image component
4. **Asset Optimization**: Minimize and compress assets

## 🔄 State Management Architecture

### Redux Store Structure

```typescript
interface RootState {
  auth: AuthState;      // Authentication state
  user: UserState;      // User profile data
  ui: UIState;          // UI state (modals, loading)
  search: SearchState;  // Search functionality
  // Add more slices as needed
}
```

### State Flow Patterns

#### 1. Async Actions with createAsyncThunk

```typescript
export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params: FetchUsersParams, { rejectWithValue }) => {
    try {
      return await userService.getUsers(params);
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);
```

#### 2. Optimistic Updates

```typescript
// Optimistically update UI, rollback on error
const updateUser = createAsyncThunk(
  'users/updateUser',
  async ({ id, data }, { dispatch, rejectWithValue }) => {
    // Optimistic update
    dispatch(userSlice.actions.updateUserOptimistic({ id, data }));
    
    try {
      return await userService.updateUser(id, data);
    } catch (error) {
      // Rollback on error
      dispatch(userSlice.actions.rollbackUserUpdate(id));
      return rejectWithValue(error.message);
    }
  }
);
```

#### 3. Normalized State

```typescript
// Normalized state structure
interface UsersState {
  entities: { [id: string]: User };
  ids: string[];
  loading: boolean;
  error: string | null;
}
```

## 🌐 API Architecture

### Proxy Layer Design

```typescript
// Generic proxy function
export async function proxyRequest(
  request: NextRequest,
  endpoint: string,
  requireAuth: boolean = true
) {
  const token = requireAuth ? extractToken(request) : null;
  
  try {
    const response = await serverHttpService.request({
      method: request.method,
      url: endpoint,
      data: await request.json(),
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    });
    
    return NextResponse.json(response.data);
  } catch (error) {
    return handleProxyError(error);
  }
}
```

### Service Layer Pattern

```typescript
// Base service class
abstract class BaseService {
  protected http: HttpService;
  
  constructor(httpService: HttpService) {
    this.http = httpService;
  }
  
  protected handleError(error: any): never {
    // Centralized error handling
    throw new ServiceError(error.message, error.status);
  }
}

// Specific service implementation
class UserService extends BaseService {
  async getUsers(): Promise<User[]> {
    try {
      return await this.http.get<User[]>('/users');
    } catch (error) {
      this.handleError(error);
    }
  }
}
```

## 🧪 Testing Architecture

### Testing Strategy

1. **Unit Tests**: Individual components and functions
2. **Integration Tests**: Component interactions
3. **E2E Tests**: Complete user workflows
4. **Visual Tests**: Component appearance

### Test Organization

```
src/
├── components/
│   └── Button/
│       ├── Button.tsx
│       ├── Button.test.tsx      # Unit tests
│       ├── Button.stories.tsx   # Storybook stories
│       └── Button.a11y.test.tsx # Accessibility tests
├── services/
│   └── userService.test.ts      # Service tests
└── e2e/
    ├── auth.spec.ts             # E2E tests
    └── user-management.spec.ts
```

## 📊 Monitoring Architecture

### Error Tracking

```typescript
// Centralized error boundary
class ErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to monitoring service
    errorTracker.captureException(error, {
      extra: errorInfo,
      tags: { component: 'ErrorBoundary' }
    });
  }
}
```

### Performance Monitoring

```typescript
// Performance tracking
export const trackPageView = (url: string) => {
  analytics.track('Page View', {
    url,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
  });
};
```

## 🔧 Build Architecture

### Build Pipeline

1. **TypeScript Compilation**: Type checking and compilation
2. **Code Splitting**: Automatic route and component splitting
3. **Asset Optimization**: Image and CSS optimization
4. **Bundle Analysis**: Size analysis and optimization
5. **Static Generation**: Pre-render static pages

### Deployment Architecture

```
Development → Build → Test → Deploy → Monitor
     ↓           ↓      ↓       ↓        ↓
   Hot Reload → Bundle → E2E → CDN → Analytics
```

## 🔮 Extensibility

### Adding New Features

1. **Create Types**: Define TypeScript interfaces
2. **Add Service**: Create API service layer
3. **Create Slice**: Add Redux state management
4. **Build Components**: Create UI components
5. **Add Routes**: Create pages and API routes
6. **Write Tests**: Add comprehensive tests

### Plugin Architecture

The template is designed to be easily extensible:

```typescript
// Plugin interface
interface Plugin {
  name: string;
  version: string;
  install(app: AppContext): void;
  uninstall(app: AppContext): void;
}

// Plugin registration
const plugins: Plugin[] = [
  new AuthPlugin(),
  new AnalyticsPlugin(),
  new ThemePlugin(),
];
```

---

This architecture provides a solid foundation for building scalable, maintainable applications while remaining flexible enough to adapt to changing requirements.
