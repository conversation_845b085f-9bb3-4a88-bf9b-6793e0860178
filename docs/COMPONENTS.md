# UI Components Documentation

This document provides comprehensive documentation for all UI components available in the Sutra-Threads template.

## 📚 Component Library Overview

The template includes 30+ pre-built components organized into categories:

- **Form Components** - Input fields, buttons, checkboxes, selects
- **Layout Components** - Headers, navigation, content wrappers
- **Data Components** - Tables, lists, data displays
- **Feedback Components** - Modals, progress indicators, badges
- **Navigation Components** - Tabs, menus, breadcrumbs

All components are:
- ✅ **TypeScript-first** with comprehensive type definitions
- ✅ **Accessible** with ARIA attributes and keyboard navigation
- ✅ **Responsive** with mobile-first design
- ✅ **Customizable** with Tailwind CSS classes
- ✅ **Consistent** following design system principles

## 🎨 Form Components

### Button

A versatile button component with multiple variants and states.

```typescript
import { Button } from '@/components/ui';

<Button
  title="Click me"
  variant="primary"
  loading={false}
  onClick={() => console.log('clicked')}
>
  Button Text
</Button>
```

**Props:**
- `title: string` - Button title (required)
- `variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost'` - Visual style
- `loading?: boolean` - Shows loading spinner
- `loadingText?: string` - Text to show when loading
- `onClick?: () => void` - Click handler
- `className?: string` - Additional CSS classes
- `height?: number` - Custom height
- `width?: number` - Custom width

### CustomInput

Advanced input component with validation, info tooltips, and password visibility.

```typescript
import { CustomInput } from '@/components/ui';

<CustomInput
  label="Email Address"
  type="email"
  value={email}
  onChange={(e) => setEmail(e.target.value)}
  placeholder="Enter your email"
  required
  showInfo
  infoText="We'll never share your email"
  error={!!emailError}
  errorMessage={emailError}
/>
```

**Props:**
- `label: string` - Input label (required)
- `value: string` - Input value (required)
- `onChange: (e: ChangeEvent<HTMLInputElement>) => void` - Change handler (required)
- `type?: string` - Input type (default: 'text')
- `placeholder?: string` - Placeholder text
- `required?: boolean` - Required field indicator
- `disabled?: boolean` - Disabled state
- `error?: boolean` - Error state
- `errorMessage?: string` - Error message to display
- `showInfo?: boolean` - Show info tooltip
- `infoText?: string` - Info tooltip text
- `className?: string` - Additional CSS classes

### Checkbox & CheckboxWithLabel

Simple checkbox components with custom styling.

```typescript
import { Checkbox, CheckboxWithLabel } from '@/components/ui';

<Checkbox
  checked={isChecked}
  onCheckedChange={setIsChecked}
/>

<CheckboxWithLabel
  checked={isChecked}
  onCheckedChange={setIsChecked}
  label="I agree to the terms"
/>
```

### Select

Dropdown selection component built on Radix UI.

```typescript
import { Select } from '@/components/ui';

<Select
  value={selectedValue}
  onValueChange={setSelectedValue}
  placeholder="Choose an option"
>
  <SelectItem value="option1">Option 1</SelectItem>
  <SelectItem value="option2">Option 2</SelectItem>
</Select>
```

### Upload

File upload component with drag-and-drop support.

```typescript
import { Upload } from '@/components/ui';

<Upload
  uploadedFile={file}
  onFileUpload={handleFileUpload}
  onClearFile={handleClearFile}
  acceptedFileTypes=".csv,.json,.xlsx"
  maxFileSize={5 * 1024 * 1024} // 5MB
  title="Upload your data file"
  description="Supported formats: CSV, JSON, XLSX"
/>
```

## 🏗️ Layout Components

### PageHeader

Consistent page header with breadcrumbs and actions.

```typescript
import { PageHeader } from '@/components/ui';

<PageHeader
  title="Dashboard"
  description="Welcome to your dashboard"
  breadcrumbs={[
    { label: 'Home', href: '/' },
    { label: 'Dashboard', href: '/dashboard' }
  ]}
  actions={[
    {
      label: 'New Item',
      onClick: handleNewItem,
      variant: 'primary',
      icon: <PlusIcon className="w-4 h-4" />
    }
  ]}
/>
```

### TopBar

Application top navigation bar.

```typescript
import { TopBar } from '@/components/ui';

<TopBar />
```

### MainContent

Main content wrapper with responsive layout.

```typescript
import { MainContent } from '@/components/ui';

<MainContent>
  <div>Your page content here</div>
</MainContent>
```

## 📊 Data Components

### Table

Advanced data table with sorting, filtering, and pagination.

```typescript
import { Table, TableHeader, TableRow, TableCell } from '@/components/ui';

const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'role', label: 'Role', sortable: false }
];

<Table
  data={users}
  columns={columns}
  sortable
  filterable
  pagination
  onSort={handleSort}
  onFilter={handleFilter}
/>
```

### TableWithColumnManagement

Table with column visibility management.

```typescript
import { TableWithColumnManagement } from '@/components/ui';

<TableWithColumnManagement
  data={data}
  columns={columns}
  initialVisibleColumns={['name', 'email']}
  onColumnVisibilityChange={handleColumnChange}
/>
```

### EditableCell & EditableSelectCell

Inline editing table cells.

```typescript
import { EditableCell, EditableSelectCell } from '@/components/ui';

<EditableCell
  value={cellValue}
  onSave={handleSave}
  type="text"
/>

<EditableSelectCell
  value={status}
  options={statusOptions}
  onSave={handleStatusChange}
/>
```

## 💬 Feedback Components

### Dialog

Modal dialog component.

```typescript
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '@/components/ui';

<Dialog>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
    </DialogHeader>
    <div>Dialog content here</div>
  </DialogContent>
</Dialog>
```

### Progress & ProgressBar

Loading and progress indicators.

```typescript
import { Progress, ProgressBar } from '@/components/ui';

<Progress value={75} max={100} />

<ProgressBar
  progress={uploadProgress}
  showPercentage
  color="blue"
/>
```

### Badge & Tag

Status and category indicators.

```typescript
import { Badge, Tag } from '@/components/ui';

<Badge variant="success">Active</Badge>
<Badge variant="warning">Pending</Badge>
<Badge variant="error">Inactive</Badge>

<Tag
  label="React"
  color="blue"
  removable
  onRemove={handleRemove}
/>
```

### Tooltip

Contextual help tooltips.

```typescript
import { Tooltip } from '@/components/ui';

<Tooltip content="This is helpful information">
  <Button>Hover me</Button>
</Tooltip>
```

## 🧭 Navigation Components

### Tabs

Tab navigation component.

```typescript
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui';

<Tabs defaultValue="tab1">
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
    <TabsTrigger value="tab2">Tab 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Content 1</TabsContent>
  <TabsContent value="tab2">Content 2</TabsContent>
</Tabs>
```

### DropdownMenu

Context menus and dropdowns.

```typescript
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui';

<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="ghost">Options</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem onClick={handleEdit}>Edit</DropdownMenuItem>
    <DropdownMenuItem onClick={handleDelete}>Delete</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### Popover

Floating content containers.

```typescript
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui';

<Popover>
  <PopoverTrigger asChild>
    <Button>Show Info</Button>
  </PopoverTrigger>
  <PopoverContent>
    <div>Additional information here</div>
  </PopoverContent>
</Popover>
```

## 🔍 Search Components

### SearchBar & SearchInput

Search functionality components.

```typescript
import { SearchBar, SearchInput } from '@/components/ui';

<SearchBar
  onSearch={handleSearch}
  placeholder="Search anything..."
/>

<SearchInput
  value={searchTerm}
  onChange={setSearchTerm}
  onSearch={handleSearch}
  debounceMs={300}
/>
```

## 🎛️ Form Controls

### Toggle

Switch/toggle component.

```typescript
import { Toggle } from '@/components/ui';

<Toggle
  pressed={isEnabled}
  onPressedChange={setIsEnabled}
  aria-label="Enable notifications"
>
  Enable
</Toggle>
```

### Radio

Radio button component.

```typescript
import { Radio } from '@/components/ui';

<Radio
  value="option1"
  checked={selectedOption === 'option1'}
  onChange={handleOptionChange}
  label="Option 1"
/>
```

### Label

Form label component.

```typescript
import { Label } from '@/components/ui';

<Label htmlFor="email">Email Address</Label>
```

## 🎨 Styling and Customization

### Theme Support

All components support theming through CSS custom properties:

```css
:root {
  --primary-color: #00B2A1;
  --secondary-color: #6B7280;
  --success-color: #10B981;
  --warning-color: #F59E0B;
  --error-color: #EF4444;
}
```

### Custom Classes

Every component accepts a `className` prop for custom styling:

```typescript
<Button className="my-custom-button-class">
  Custom Button
</Button>
```

### Responsive Design

Components are built with mobile-first responsive design:

```typescript
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
  {/* Responsive grid */}
</div>
```

## 🔧 Component Development

### Creating Custom Components

Follow these patterns when creating new components:

1. **Use TypeScript interfaces** for props
2. **Support common props** like `className`, `children`
3. **Provide sensible defaults** for optional props
4. **Use Tailwind CSS** for styling
5. **Include accessibility** attributes
6. **Export from index files** for clean imports

### Component Template

```typescript
"use client";

import React from 'react';

interface MyComponentProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

export const MyComponent = ({
  variant = 'primary',
  size = 'md',
  className = '',
  children,
  ...props
}: MyComponentProps) => {
  const baseClasses = 'component-base-styles';
  const variantClasses = {
    primary: 'primary-styles',
    secondary: 'secondary-styles'
  };
  const sizeClasses = {
    sm: 'small-styles',
    md: 'medium-styles',
    lg: 'large-styles'
  };

  return (
    <div 
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};
```

## 📖 Usage Examples

For complete usage examples and live demos, check out:

- **Demo Pages**: `/src/app/table-demo/` and `/src/app/dashboard/`
- **Component Stories**: Individual component directories
- **Integration Examples**: How components work together in real applications

---

This component library provides a solid foundation for building consistent, accessible, and beautiful user interfaces. Each component is designed to be flexible and customizable while maintaining design system consistency.
