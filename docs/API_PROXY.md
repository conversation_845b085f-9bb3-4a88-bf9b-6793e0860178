# Next.js API Proxy Setup

This document explains the API proxy architecture implemented in this Next.js application.

## Overview

The application uses a **proxy pattern** where:
- **Frontend** makes requests to Next.js API routes (`/api/*`)
- **Next.js API routes** proxy requests to the external backend API
- **Authentication tokens** are handled server-side in the proxy layer

## Architecture

```
Frontend (NEXTjs) → Next.js API Routes → External Backend API
     ↓                    ↓                      ↓
  /api/auth/verify  →  /auth/verify  →  External API
  /api/users/profile → /users/profile → External API
```

## Benefits

1. **Security**: Sensitive API URLs and configurations are hidden from the client
2. **CORS**: No CORS issues since frontend calls same-origin API routes
3. **Token Management**: Server-side token handling and validation
4. **Caching**: Potential for server-side caching of API responses
5. **Error Handling**: Centralized error handling and response formatting
6. **Rate Limiting**: Can implement rate limiting at the proxy layer

## Configuration

### Environment Variables

```bash
# External API URL (used by server-side proxy)
NEXT_PUBLIC_API_BASE_URL=http://localhost:5001/api

# SSO Login URL
NEXT_PUBLIC_SSO_LOGIN_URL=https://sutra.ai/login

# App URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### API Constants

- `EXTERNAL_API_URL`: Points to the external backend API
- `INTERNAL_API_URL`: Points to Next.js API routes (`/api`)

## Already Available Endpoints (Extend as needed)

### Authentication

| Method | Internal Route | External Route | Description |
|--------|---------------|----------------|-------------|
| GET | `/api/auth/verify` | `/auth/verify` | Verify auth token |
| POST | `/api/auth/refresh` | `/auth/refresh` | Refresh auth token |

### Health Check

| Method | Internal Route | Description |
|--------|---------------|-------------|
| GET | `/api/health` | API proxy health check |

## Usage Examples

### Frontend Service Calls

```typescript
// authService.ts
export const authService = {
  async verifyToken(token: string): Promise<boolean> {
    const response = await httpService.get<{ valid: boolean }>(EXTERNAL_ENDPOINTS.AUTH.VERIFY, { 
      params: { authToken: token } 
    });
    return response.valid;
  }
};

```

### Authentication Flow

1. Frontend receives token from URL or storage
2. Frontend calls `/api/auth/verify?authToken=<token>`
3. Next.js proxy extracts token and calls external `/auth/verify`
4. External API validates token and returns result
5. Proxy returns formatted response to frontend

### Authenticated Requests

1. Frontend includes `Authorization: Bearer <token>` header
2. Next.js proxy extracts token from header
3. Proxy forwards request to external API with token
4. External API processes authenticated request
5. Proxy returns response to frontend

## File Structure

```
src/
├── app/api/                    # Next.js API routes
│   ├── auth/
│   │   ├── verify/route.ts     # Auth verification proxy
│   │   └── refresh/route.ts    # Token refresh proxy
│   ├── users/
│   │   ├── profile/route.ts    # User profile proxy
│   │   └── [userId]/route.ts   # User by ID proxy
│   └── health/route.ts         # Health check
├── lib/
│   ├── serverHttp.ts           # Server-side HTTP client
│   └── apiProxy.ts             # Proxy utility functions
├── services/
│   ├── http.ts                 # Frontend HTTP client
│   ├── authService.ts          # Auth service (uses proxy)
│   └── userService.ts          # User service (uses proxy)
└── constants/
    ├── api.ts                  # API URL constants
    └── endpoints.ts            # Endpoint definitions
```

## Error Handling

The proxy layer provides standardized error responses:

```json
{
  "error": "Error message",
  "details": { /* Additional error details */ },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Security Considerations

1. **Token Validation**: Tokens are validated server-side
2. **HTTPS**: Use HTTPS in production for all API calls
3. **Rate Limiting**: Consider implementing rate limiting
4. **CORS**: Properly configure CORS for external API
5. **Environment Variables**: Keep sensitive URLs in environment variables

## Development

### Adding New Endpoints

1. Create Next.js API route in `src/app/api/`
2. Use proxy utilities from `src/lib/apiProxy.ts`
3. Add endpoint to `src/constants/endpoints.ts`
4. Update frontend service to use new endpoint
5. Update this documentation

### Testing

```bash
# Test health endpoint
curl http://localhost:3000/api/health

# Test auth verification
curl "http://localhost:3000/api/auth/verify?authToken=<token>"

# Test authenticated endpoint
curl -H "Authorization: Bearer <token>" http://localhost:3000/api/users/profile
```
