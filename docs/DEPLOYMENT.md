# Deployment Guide

This guide covers deploying the Sutra-Threads Next.js application to various platforms and environments.

## 🚀 Quick Deployment Options

### Vercel (Recommended)

Vercel is the easiest way to deploy Next.js applications:

1. **Connect your repository** to Vercel
2. **Configure environment variables** in Vercel dashboard
3. **Deploy automatically** on every push to main branch

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new)

### Netlify

Deploy to Netlify with automatic builds:

1. **Connect your Git repository**
2. **Set build command**: `npm run build`
3. **Set publish directory**: `.next`
4. **Configure environment variables**

### Docker

Use the included Dockerfile for containerized deployment:

```bash
# Build the image
docker build -t sutra-threads-ui .

# Run the container
docker run -p 3000:3000 sutra-threads-ui
```

## 🔧 Environment Configuration

### Required Environment Variables

Create these environment variables in your deployment platform:

```env
# Production API URL
NEXT_PUBLIC_API_BASE_URL=https://your-api.com/api

# SSO Login URL (if using external auth)
NEXT_PUBLIC_SSO_LOGIN_URL=https://your-sso-provider.com/login

# Your application URL
NEXT_PUBLIC_APP_URL=https://your-app.com

# Optional: Analytics and monitoring
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
```

### Platform-Specific Configuration

#### Vercel

1. Go to your project dashboard
2. Navigate to **Settings** → **Environment Variables**
3. Add each variable with appropriate values
4. Set different values for **Development**, **Preview**, and **Production**

#### Netlify

1. Go to **Site settings** → **Environment variables**
2. Add variables in the **Environment variables** section
3. Deploy to apply changes

#### AWS/Azure/GCP

Use your platform's secret management service:
- **AWS**: Systems Manager Parameter Store or Secrets Manager
- **Azure**: Key Vault
- **GCP**: Secret Manager

## 🏗️ Build Configuration

### Next.js Configuration

The `next.config.ts` file includes production optimizations:

```typescript
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Enable static optimization
  output: 'standalone',
  
  // Optimize images
  images: {
    domains: ['your-cdn.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Enable compression
  compress: true,
  
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
```

### Build Scripts

Package.json includes optimized build scripts:

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  }
}
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose

For local development with backend services:

```yaml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=http://backend:5000/api
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
    depends_on:
      - backend

  backend:
    image: your-backend-image
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=******************************/mydb
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=mydb
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## ☁️ Cloud Platform Deployment

### AWS (Amplify)

1. **Connect repository** to AWS Amplify
2. **Configure build settings**:
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```
3. **Set environment variables** in Amplify console

### Google Cloud Platform (Cloud Run)

1. **Build and push Docker image**:
   ```bash
   gcloud builds submit --tag gcr.io/PROJECT-ID/sutra-threads-ui
   ```

2. **Deploy to Cloud Run**:
   ```bash
   gcloud run deploy --image gcr.io/PROJECT-ID/sutra-threads-ui --platform managed
   ```

### Azure (Container Instances)

1. **Build and push to Azure Container Registry**:
   ```bash
   az acr build --registry myregistry --image sutra-threads-ui .
   ```

2. **Deploy to Container Instances**:
   ```bash
   az container create \
     --resource-group myResourceGroup \
     --name sutra-threads-ui \
     --image myregistry.azurecr.io/sutra-threads-ui:latest
   ```

## 🔒 Security Considerations

### Environment Variables

- **Never commit** `.env` files to version control
- **Use platform secret management** for sensitive data
- **Rotate secrets** regularly
- **Limit access** to production environment variables

### Content Security Policy

Add CSP headers for enhanced security:

```typescript
// next.config.ts
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline';
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: https:;
      font-src 'self';
    `.replace(/\s{2,}/g, ' ').trim()
  }
];
```

### HTTPS Configuration

Ensure HTTPS is enforced:

```typescript
// Redirect HTTP to HTTPS
async redirects() {
  return [
    {
      source: '/(.*)',
      has: [
        {
          type: 'header',
          key: 'x-forwarded-proto',
          value: 'http',
        },
      ],
      destination: 'https://your-domain.com/:path*',
      permanent: true,
    },
  ];
}
```

## 📊 Performance Optimization

### Build Optimization

1. **Enable static optimization** where possible
2. **Use Image Optimization** with Next.js Image component
3. **Implement code splitting** with dynamic imports
4. **Enable compression** in production

### CDN Configuration

Configure CDN for static assets:

```typescript
// next.config.ts
const nextConfig = {
  assetPrefix: process.env.NODE_ENV === 'production' 
    ? 'https://cdn.your-domain.com' 
    : '',
  
  images: {
    loader: 'custom',
    loaderFile: './src/lib/imageLoader.js',
  },
};
```

### Caching Strategy

Implement proper caching headers:

```typescript
// next.config.ts
async headers() {
  return [
    {
      source: '/static/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=31536000, immutable',
        },
      ],
    },
  ];
}
```

## 🔍 Monitoring and Analytics

### Error Tracking

Integrate error tracking service:

```typescript
// src/lib/errorTracking.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
});
```

### Performance Monitoring

Add performance monitoring:

```typescript
// src/lib/analytics.ts
export const trackPageView = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
      page_path: url,
    });
  }
};
```

### Health Checks

Implement health check endpoints:

```typescript
// src/app/api/health/route.ts
export async function GET() {
  return Response.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
  });
}
```

## 🚦 CI/CD Pipeline

### GitHub Actions

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_API_BASE_URL: ${{ secrets.API_BASE_URL }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.APP_URL }}
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

### GitLab CI

```yaml
stages:
  - build
  - test
  - deploy

build:
  stage: build
  script:
    - npm ci
    - npm run build
  artifacts:
    paths:
      - .next/

test:
  stage: test
  script:
    - npm run lint
    - npm run type-check

deploy:
  stage: deploy
  script:
    - echo "Deploy to production"
  only:
    - main
```

## 🔧 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all environment variables are set
   - Clear `.next` cache and rebuild

2. **Runtime Errors**
   - Check browser console for client-side errors
   - Review server logs for API issues
   - Verify environment variable values

3. **Performance Issues**
   - Enable Next.js bundle analyzer
   - Check for large dependencies
   - Optimize images and assets

### Debug Commands

```bash
# Analyze bundle size
npm install --save-dev @next/bundle-analyzer
ANALYZE=true npm run build

# Check for unused dependencies
npm install --save-dev depcheck
npx depcheck

# Audit security vulnerabilities
npm audit
```

## 📋 Deployment Checklist

Before deploying to production:

- [ ] All environment variables configured
- [ ] HTTPS enabled and enforced
- [ ] Security headers implemented
- [ ] Error tracking configured
- [ ] Performance monitoring setup
- [ ] Health checks implemented
- [ ] Backup strategy in place
- [ ] CI/CD pipeline tested
- [ ] Load testing completed
- [ ] Documentation updated

---

This deployment guide provides comprehensive instructions for deploying your Sutra-Threads application to production environments with proper security, performance, and monitoring considerations.
